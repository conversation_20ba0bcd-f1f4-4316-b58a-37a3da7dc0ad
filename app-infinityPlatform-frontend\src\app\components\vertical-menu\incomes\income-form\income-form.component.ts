import { Compo<PERSON>, <PERSON>Ini<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormsModule,
  ReactiveFormsModule
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputNumberModule } from 'primeng/inputnumber';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { DialogService as PrimeDialogService, DynamicDialogModule } from 'primeng/dynamicdialog';
import { TooltipModule } from 'primeng/tooltip';
import { Client } from '../../../../dtos/client.model';
import { Income, IncomeType } from '../../../../dtos/income.model';
import { IncomeService } from '../../../../services/income.service';
import { ClientService } from '../../../../services/client.service';
import { NavigationService } from '../../../../services/navigation.service';
import { DialogService } from '../../../../services/dialog.service';
import { UserService } from '../../../../services/user.service';
import { User } from '../../../../dtos/user.model';
import { HeaderService } from '../../../../services/header.service';
// ✅ IMPORTAR EL COMPONENTE DE DIÁLOGO GENÉRICO
import { GenericDialogComponent } from '../../../shared/generic-dialog/generic-dialog.component';
// ✅ IMPORTAR EL COMPONENTE DE CONTADOR DE DINERO
import {
  MoneyCounterComponent,
  MoneyCounterResult,
} from '../../money-counter-modal/money-counter-modal.component';

@Component({
  selector: 'app-income-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    InputNumberModule,
    DropdownModule,
    CalendarModule,
    ToastModule,
    TooltipModule,
    DynamicDialogModule,
    GenericDialogComponent // ✅ AGREGAR EL COMPONENTE DE DIÁLOGO GENÉRICO
  ],
  providers: [MessageService, PrimeDialogService],
  templateUrl: './income-form.component.html',
  styleUrl: './income-form.component.css',
})
export class IncomeFormComponent implements OnInit, OnDestroy {
  incomeForm: FormGroup;
  isLoading: boolean = false;
  isEditMode: boolean = false;
  incomeId: number | null = null;

  clients: Client[] = [];
  incomeTypes: IncomeType[] = [];
  currentUser: User | null = null;
  currentUserName: string = '';

  constructor(
    private fb: FormBuilder,
    private incomeService: IncomeService,
    private clientService: ClientService,
    private navigationService: NavigationService,
    private route: ActivatedRoute,
    private router: Router,
    private messageService: MessageService,
    private userService: UserService,
    private headerService: HeaderService,
    private dialogService: DialogService, // ✅ AGREGAR EL SERVICIO DE DIÁLOGO GENÉRICO
    private primeDialogService: PrimeDialogService // ✅ AGREGAR EL SERVICIO DE DIÁLOGO DE PRIMENG
  ) {
    this.incomeForm = this.fb.group({
      clientId: [null, Validators.required],
      incomeTypeId: [null, Validators.required],
      amount: [0.01, [Validators.required, Validators.min(0.01)]],
      description: ['', [Validators.required, Validators.maxLength(255)]],
      userId: [null] // Agregar campo para userId
    });
  }

  ngOnInit(): void {
    // Verificar si estamos en modo edición comprobando la ruta
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.incomeId = +id;
        this.isEditMode = true;
        console.log('Modo edición activado para income ID:', this.incomeId);
      }

      // Cargar información del usuario logueado primero
      this.loadCurrentUser();

      // Cargar clientes y tipos de ingreso
      this.loadClients();
      this.loadIncomeTypes();
    });
  }

  ngOnDestroy(): void {
    // Limpiar cualquier recurso si es necesario
  }

  // Método simplificado para cargar clientes desde la API
  loadClients(): void {
    this.isLoading = true;
    this.clientService.getAllClients().subscribe({
      next: (response) => {
        if (response.code === 200) {
          // Los datos están directamente en response.data, no en response.data.content
          this.clients = Array.isArray(response.data) ? response.data : [];
          console.log('Clientes cargados:', this.clients.length);
        } else {
          // ✅ USAR EL DIÁLOGO GENÉRICO EN LUGAR DE showError
          this.dialogService.showError('Error al cargar clientes', response.message as string);
        }
        this.isLoading = false;
      },
      error: (err) => {
        // ✅ USAR EL DIÁLOGO GENÉRICO EN LUGAR DE showError
        this.dialogService.showError('Error al cargar clientes', 'No se pudieron cargar los clientes');
        this.isLoading = false;
        console.error('Error loading clients:', err);
      },
    });
  }

  // Método simplificado para cargar tipos de ingreso desde la API
  loadIncomeTypes(): void {
    this.isLoading = true;
    this.incomeService.getIncomeTypes().subscribe({
      next: (response) => {
        if (response.code === 200) {
          this.incomeTypes = Array.isArray(response.data) ? response.data : [];
          console.log('Tipos de ingreso cargados:', this.incomeTypes.length);
        } else {
          // ✅ USAR EL DIÁLOGO GENÉRICO EN LUGAR DE showError
          this.dialogService.showError('Error al cargar tipos de ingreso', response.message as string);
        }
        this.isLoading = false;
      },
      error: (err) => {
        // ✅ USAR EL DIÁLOGO GENÉRICO EN LUGAR DE showError
        this.dialogService.showError('Error al cargar tipos de ingreso', 'No se pudieron cargar los tipos de ingreso');
        this.isLoading = false;
        console.error('Error loading income types:', err);
      },
    });
  }

  // Método para cargar la información del usuario logueado
  loadCurrentUser(): void {
    const userInfo = this.headerService.getCurrentUser();
    if (userInfo && userInfo.email) {
      // Buscar el usuario completo por email para obtener el ID
      this.userService.searchUserByEmail(userInfo.email).subscribe({
        next: (response) => {
          if (response.code === 200 && response.data) {
            this.currentUser = response.data;
            this.currentUserName = `${this.currentUser?.firstName || ''} ${this.currentUser?.lastName || ''}`;

            // Establecer el userId en el formulario siempre (tanto en creación como edición)
            this.incomeForm.patchValue({
              userId: this.currentUser?.id || null
            });

            console.log('Usuario actual cargado:', this.currentUser);
            console.log(
              'UserId establecido en formulario:',
              this.currentUser?.id || 'No disponible'
            );

            // DESPUÉS de cargar el usuario, cargar el income si estamos en modo edición
            if (this.isEditMode && this.incomeId) {
              console.log('Cargando income para edición...');
              this.loadIncomeDetails(this.incomeId);
            }
          } else {
            // ✅ USAR EL DIÁLOGO GENÉRICO
            this.dialogService.showError('Error al cargar usuario', 'No se pudo obtener la información del usuario logueado');
          }
        },
        error: (err) => {
          // ✅ USAR EL DIÁLOGO GENÉRICO
          this.dialogService.showError('Error al cargar usuario', 'No se pudo obtener la información del usuario logueado');
          console.error('Error loading current user:', err);
        },
      });
    } else {
      // ✅ USAR EL DIÁLOGO GENÉRICO
      this.dialogService.showError('Error de autenticación', 'No se pudo obtener la información del usuario logueado');
    }
  }

  loadIncomeDetails(incomeId: number): void {
    console.log('Iniciando carga de income con ID:', incomeId);
    this.isLoading = true;

    this.incomeService.getIncomeById(incomeId).subscribe({
      next: (response) => {
        console.log('Respuesta del servidor al cargar income:', response);

        if (response.code === 200) {
          const income = response.data;
          console.log('Income cargado:', income);
          console.log('Usuario actual ID:', this.currentUser?.id);
          console.log('Income userId:', income.userId);

          // Cargar los datos en el formulario
          this.incomeForm.patchValue({
            clientId: income.clientId,
            incomeTypeId: income.incomeTypeId,
            amount: income.amount,
            description: income.description,
            userId: this.currentUser?.id || income.userId // Usar currentUser si está disponible
          });

          console.log('Formulario actualizado con datos del income');
          console.log('Valores del formulario:', this.incomeForm.value);
        } else {
          // ✅ USAR EL DIÁLOGO GENÉRICO
          this.dialogService.showError('Error al cargar ingreso', response.message as string);
          this.navigateToIncomes();
        }
        this.isLoading = false;
      },
      error: (err) => {
        // ✅ USAR EL DIÁLOGO GENÉRICO
        this.dialogService.showError('Error al cargar ingreso', 'No se pudo cargar el ingreso');
        this.isLoading = false;
        console.error('Error loading income:', err);
        this.navigateToIncomes();
      },
    });
  }

  // ✅ MÉTODO ACTUALIZADO PARA USAR CONFIRMACIONES CON EL DIÁLOGO GENÉRICO
  async saveIncome(): Promise<void> {
    // Verificar que el usuario actual esté cargado
    if (!this.currentUser || !this.currentUser.id) {
      this.dialogService.showError('Error de usuario', 'No se ha cargado la información del usuario. Por favor, recargue la página.');
      return;
    }

    // Asegurar que el userId esté establecido en el formulario
    if (!this.incomeForm.get('userId')?.value) {
      this.incomeForm.patchValue({
        userId: this.currentUser.id,
      });
    }

    if (this.incomeForm.valid) {
      const incomeData = this.incomeForm.value;

      // Verificación adicional antes de enviar
      if (!incomeData.userId) {
        incomeData.userId = this.currentUser.id;
      }

      console.log('Datos del ingreso a enviar:', incomeData);

      // ✅ CREAR DESCRIPCIÓN DETALLADA PARA LA CONFIRMACIÓN
      const incomeDescription = `el ingreso "${incomeData.description}" por $${incomeData.amount.toFixed(2)}`;

      // ✅ MOSTRAR DIÁLOGO DE CONFIRMACIÓN ANTES DE GUARDAR
      let confirmed = false;
      if (this.isEditMode && this.incomeId) {
        confirmed = await this.dialogService.showUpdateConfirmation('ingreso', incomeDescription);
      } else {
        confirmed = await this.dialogService.showCreateConfirmation('ingreso', incomeDescription);
      }

      if (!confirmed) {
        return; // El usuario canceló la operación
      }

      this.isLoading = true;

      if (this.isEditMode && this.incomeId) {
        // Actualizar ingreso existente
        this.incomeService
          .updateIncome(this.incomeId, incomeData)
          .subscribe({
            next: (response) => {
              if (
                response.code === 200 ||
                response.code === 201 ||
                (response.message &&
                  response.message.toLowerCase().includes('success'))
              ) {
                // ✅ USAR EL DIÁLOGO GENÉRICO PARA ÉXITO
                this.dialogService.showUpdateSuccess('ingreso');
                this.navigateToIncomes();
              } else {
                // ✅ USAR EL DIÁLOGO GENÉRICO PARA ERROR
                this.dialogService.showUpdateError('ingreso', response.message as string);
              }
              this.isLoading = false;
            },
            error: (err) => {
              // ✅ USAR EL DIÁLOGO GENÉRICO PARA ERROR
              this.dialogService.showUpdateError('ingreso', 'No se pudo actualizar el ingreso');
              this.isLoading = false;
              console.error('Error updating income:', err);
            },
          });
      } else {
        // Crear nuevo ingreso
        this.incomeService.createIncome(incomeData).subscribe({
          next: (response) => {
            if (
              response.code === 200 ||
              response.code === 201 ||
              (response.message &&
                response.message.toLowerCase().includes('success'))
            ) {
              // ✅ USAR EL DIÁLOGO GENÉRICO PARA ÉXITO
              this.dialogService.showCreateSuccess('ingreso');
              this.navigateToIncomes();
            } else {
              // ✅ USAR EL DIÁLOGO GENÉRICO PARA ERROR
              this.dialogService.showCreateError('ingreso', response.message as string);
            }
            this.isLoading = false;
          },
          error: (err) => {
            // ✅ USAR EL DIÁLOGO GENÉRICO PARA ERROR
            this.dialogService.showCreateError('ingreso', 'No se pudo crear el ingreso');
            this.isLoading = false;
            console.error('Error creating income:', err);
          },
        });
      }
    } else {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.incomeForm.controls).forEach((key) => {
        this.incomeForm.get(key)?.markAsTouched();
      });
      // ✅ USAR EL DIÁLOGO GENÉRICO PARA ADVERTENCIA
      this.dialogService.showWarning('Por favor, complete todos los campos requeridos');
    }
  }

  // ✅ MANTENER ESTOS MÉTODOS PARA COMPATIBILIDAD
  // Método para mostrar un mensaje de éxito
  showSuccess(detail: string): void {
    this.messageService.add({
      severity: 'success',
      summary: 'Éxito',
      detail,
      life: 3000,
    });
  }

  showError(summary: string, detail: string): void {
    this.messageService.add({
      severity: 'error',
      summary: summary,
      detail,
      life: 5000,
    });
  }

  // Método para navegar al menú principal
  navigateToMainMenu(): void {
    setTimeout(() => {
      this.navigateToIncomes();
      console.log('Navegando a la lista de incomes');
    }, 300);
  }

  navigateToIncomes(): void {
    setTimeout(() => {
      this.router.navigate(['/main-cloud/incomes']);
      console.log('Navegando a la lista de incomes');
    }, 300);
  }

  navigateBack(): void {
    this.navigateToIncomes();
  }

  /**
   * Abre el diálogo de contador de dinero
   */
  openMoneyCounter(): void {
    // Obtener el valor actual del campo amount
    const currentAmount = this.incomeForm.get('amount')?.value || 0;

    const ref = MoneyCounterComponent.openMoneyCounter(this.primeDialogService, {
      currentAmount: currentAmount,
      title: 'Contar Dinero para Ingreso',
      instructionMessage:
        'Seleccione las denominaciones para calcular el monto del ingreso',
    });

    ref.onClose.subscribe((result: MoneyCounterResult) => {
      if (result) {
        // Actualizar el valor del formulario con el monto contado
        this.incomeForm.get('amount')?.setValue(result.totalAmount);
      }
    });
  }
}

