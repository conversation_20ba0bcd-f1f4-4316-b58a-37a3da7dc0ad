:host {
  --primary-color: #1d3c34;
  --primary-hover: #0f2a23;
  --primary-light: #e8f0ed;
  --text-color: #2c2c2c;
  --text-light: #5f5f5f;
  --text-lighter: #8a8a8a;
  --background: #f5f5f0;
  --background-card: #ffffff;
  --background-hover: #f0efe6;
  --border-color: #d8d3c5;
  --accent-color: #b8860b;
  --accent-light: #d4af37;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;
  --transition: all 0.3s ease;
  --success-color: #198754;
  --danger-color: #dc3545;
  --info-color: #0dcaf0;
  --warning-color: #ffc107;
}

/* Estilos para el diálogo de PrimeNG */
:host ::ng-deep .client-detail-dialog .p-dialog-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: 1.25rem 1.5rem;
  font-family: "Playfair Display", Georgia, serif;
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
}

:host ::ng-deep .client-detail-dialog .p-dialog-content {
  padding: 1.5rem;
  background-color: var(--background-card);
  font-family: "Playfair Display", Georgia, serif;
}

:host ::ng-deep .client-detail-dialog .p-dialog-footer {
  padding: 1rem 1.5rem;
  background-color: var(--background);
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
  border-top: 1px solid var(--border-color);
}

/* Contenedor principal */
.client-detail-container {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* Encabezado con ID */
.client-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.client-id {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.id-label {
  font-weight: 600;
  color: var(--text-light);
}

.id-value {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* Sección de información principal */
.main-info-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--background);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

/* Estilos para los items de detalle */
.client-detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-label {
  font-weight: 600;
  color: var(--text-light);
  font-size: 0.95rem;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.05rem;
  color: var(--text-color);
}

/* Iconos */
.link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius);
  color: white;
  font-size: 1.125rem;
}

/* Estilos para las secciones desplegables */
.collapsible-section {
  margin-bottom: 1.25rem;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  background-color: var(--background-card);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.collapsible-section:hover {
  box-shadow: var(--shadow);
}

/* Cabecera de la sección desplegable */
.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: var(--background);
  cursor: pointer;
  transition: var(--transition);
  user-select: none;
}

.collapsible-header:hover {
  background-color: var(--background-hover);
}

.collapsible-header .section-title {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1.05rem;
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

/* Icono de la sección desplegable */
.collapsible-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: var(--radius-full);
  background-color: var(--primary-light);
  color: var(--primary-color);
  transition: var(--transition);
}

.collapsible-icon i {
  transition: transform 0.3s ease;
}

.collapsible-icon.expanded i {
  transform: rotate(180deg);
}

/* Contenido de la sección desplegable */
.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  padding: 0 1rem;
}

.collapsible-content.expanded {
  max-height: 800px; /* Valor alto para asegurar que todo el contenido se muestre */
  padding: 1rem;
}

/* Secciones específicas */
.client-contact-section,
.client-fiscal-section,
.client-dates-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Estado del cliente */
.client-status-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--background);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.875rem;
}

.status-active {
  background-color: rgba(25, 135, 84, 0.15);
  color: rgba(25, 135, 84, 1);
}

.status-inactive {
  background-color: rgba(220, 53, 69, 0.15);
  color: rgba(220, 53, 69, 1);
}

/* Botones del footer */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.close-button, .edit-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius);
  border: none;
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.close-button {
  background-color: var(--background);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.close-button:hover {
  background-color: var(--background-hover);
}

.edit-button {
  background: linear-gradient(135deg, var(--warning-color), #e6a700);
  color: white;
}

.edit-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Gradientes para los iconos */
.bg-gradient-elegant-1 {
  background: linear-gradient(135deg, #3a8fb7, #1a6985);
}

.bg-gradient-elegant-2 {
  background: linear-gradient(135deg, #4caf50, #2e7d32);
}

.bg-gradient-elegant-3 {
  background: linear-gradient(135deg, #9c27b0, #6a1b9a);
}

.bg-gradient-elegant-4 {
  background: linear-gradient(135deg, #ff9800, #e65100);
}

.bg-gradient-elegant-5 {
  background: linear-gradient(135deg, #795548, #4e342e);
}

.bg-gradient-elegant-6 {
  background: linear-gradient(135deg, #607d8b, #37474f);
}

.bg-gradient-elegant-7 {
  background: linear-gradient(135deg, #e91e63, #ad1457);
}

.bg-gradient-elegant-8 {
  background: linear-gradient(135deg, #673ab7, #4527a0);
}

.bg-gradient-elegant-9 {
  background: linear-gradient(135deg, #3f51b5, #283593);
}

.bg-gradient-elegant-10 {
  background: linear-gradient(135deg, #2196f3, #1565c0);
}

.bg-gradient-elegant-11 {
  background: linear-gradient(135deg, #00bcd4, #00838f);
}

.bg-gradient-elegant-12 {
  background: linear-gradient(135deg, #009688, #00695c);
}

.bg-gradient-elegant-13 {
  background: linear-gradient(135deg, #8bc34a, #558b2f);
}

/* Animaciones y efectos */
.client-detail-container {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Efecto suave al expandir */
.collapsible-section {
  transition: box-shadow 0.3s ease;
}

.collapsible-section:has(.collapsible-content.expanded) {
  box-shadow: var(--shadow);
}

/* Estilos responsivos */
@media (max-width: 768px) {
  .client-detail-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .detail-label,
  .detail-value {
    width: 100%;
  }

  .detail-label {
    margin-bottom: 0.25rem;
  }

  :host ::ng-deep .client-detail-dialog {
    width: 90% !important;
    max-width: 550px !important;
  }
}

/* Mejoras de accesibilidad */
.collapsible-header:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.close-button:focus,
.edit-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Estilos para los tooltips */
:host ::ng-deep .p-tooltip .p-tooltip-text {
  background-color: var(--text-color);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  box-shadow: var(--shadow);
}

:host ::ng-deep .p-tooltip .p-tooltip-arrow {
  border-top-color: var(--text-color);
}

/* Estilos para el scroll dentro del diálogo */
:host ::ng-deep .p-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}

:host ::ng-deep .p-dialog-content::-webkit-scrollbar {
  width: 6px;
}

:host ::ng-deep .p-dialog-content::-webkit-scrollbar-track {
  background: transparent;
}

:host ::ng-deep .p-dialog-content::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 6px;
}

/* Estilos para el modo oscuro si se implementa */
@media (prefers-color-scheme: dark) {
  :host-context(.dark-theme) {
    --background-card: #2c2c2c;
    --background: #1a1a1a;
    --background-light: rgba(255, 255, 255, 0.05);
    --background-hover: rgba(255, 255, 255, 0.1);
    --text-color: #f5f5f5;
    --text-light: #d1d1d1;
    --text-lighter: #a0a0a0;
    --border-color: rgba(255, 255, 255, 0.1);
  }
}
