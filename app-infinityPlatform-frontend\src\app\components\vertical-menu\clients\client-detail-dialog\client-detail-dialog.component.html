<p-dialog [(visible)]="visible" [modal]="true" [draggable]="false" [resizable]="false" [style]="{width: '550px'}"
  header="Detalles del Cliente" styleClass="client-detail-dialog" [closeOnEscape]="true" [dismissableMask]="true"
  (onHide)="visibleChange.emit(false)">
  
  <ng-container *ngIf="client">
    <div class="client-detail-container">
      <!-- Encabezado con ID -->
      <div class="client-header">
        <div class="client-id">
          <span class="id-label">ID:</span>
          <span class="id-value">{{ client.clientId }}</span>
        </div>
      </div>
      
      <!-- Información principal -->
      <div class="main-info-section">
        <div class="client-detail-item">
          <div class="detail-label">Empresa:</div>
          <div class="detail-value">
            <div class="link-icon bg-gradient-elegant-1">
              <i class="bi bi-building"></i>
            </div>
            <span>{{ client.companyName }}</span>
          </div>
        </div>
        
        <div class="client-detail-item">
          <div class="detail-label">NIT:</div>
          <div class="detail-value">
            <div class="link-icon bg-gradient-elegant-2">
              <i class="bi bi-card-text"></i>
            </div>
            <span>{{ client.taxId }} - {{ client.verificationNumber }}</span>
          </div>
        </div>
      </div>
      
      <!-- Sección de información de contacto - DESPLEGABLE -->
      <div class="collapsible-section">
        <div class="collapsible-header" (click)="toggleContactInfo()">
          <div class="section-title">Información de contacto</div>
          <div class="collapsible-icon" [class.expanded]="contactInfoExpanded">
            <i class="bi bi-chevron-down"></i>
          </div>
        </div>
        
        <div class="collapsible-content" [class.expanded]="contactInfoExpanded">
          <div class="client-contact-section">
            <!-- Contacto -->
            <div class="client-detail-item">
              <div class="detail-label">Contacto:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-3">
                  <i class="bi bi-person"></i>
                </div>
                <span>{{ client.contactName }}</span>
              </div>
            </div>
            
            <!-- Dirección -->
            <div class="client-detail-item">
              <div class="detail-label">Dirección:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-4">
                  <i class="bi bi-geo-alt"></i>
                </div>
                <span>{{ client.address }}</span>
              </div>
            </div>
            
            <!-- Teléfono -->
            <div class="client-detail-item">
              <div class="detail-label">Teléfono:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-5">
                  <i class="bi bi-telephone"></i>
                </div>
                <span>{{ client.countryCode }} {{ client.phone }}</span>
              </div>
            </div>
            
            <!-- Email -->
            <div class="client-detail-item">
              <div class="detail-label">Email:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-6">
                  <i class="bi bi-envelope"></i>
                </div>
                <span>{{ client.email }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Sección de información fiscal - DESPLEGABLE -->
      <div class="collapsible-section">
        <div class="collapsible-header" (click)="toggleFiscalInfo()">
          <div class="section-title">Información fiscal</div>
          <div class="collapsible-icon" [class.expanded]="fiscalInfoExpanded">
            <i class="bi bi-chevron-down"></i>
          </div>
        </div>
        
        <div class="collapsible-content" [class.expanded]="fiscalInfoExpanded">
          <div class="client-fiscal-section">
            <!-- Razón Social -->
            <div class="client-detail-item">
              <div class="detail-label">Razón Social:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-7">
                  <i class="bi bi-briefcase"></i>
                </div>
                <span>{{ client.businessName }}</span>
              </div>
            </div>
            
            <!-- IVA -->
            <div class="client-detail-item">
              <div class="detail-label">Tasa de IVA:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-8">
                  <i class="bi bi-percent"></i>
                </div>
                <span>{{ client.vatRate }}%</span>
              </div>
            </div>

            <!-- Impuesto al consumo -->
            <div class="client-detail-item">
              <div class="detail-label">Tasa de impuesto al consumo:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-9">
                  <i class="bi bi-cash"></i>
                </div>
                <span>{{ client.consumptionTaxRate }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Sección de fechas - DESPLEGABLE -->
      <div class="collapsible-section">
        <div class="collapsible-header" (click)="toggleDatesInfo()">
          <div class="section-title">Fechas</div>
          <div class="collapsible-icon" [class.expanded]="datesInfoExpanded">
            <i class="bi bi-chevron-down"></i>
          </div>
        </div>
        
        <div class="collapsible-content" [class.expanded]="datesInfoExpanded">
          <div class="client-dates-section">
            <!-- Fecha de nacimiento -->
            <div class="client-detail-item">
              <div class="detail-label">Fecha de nacimiento:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-10">
                  <i class="bi bi-calendar-event"></i>
                </div>
                <span>{{ client.birthDate | date:'dd/MM/yyyy' }}</span>
              </div>
            </div>

            <!-- Fecha de registro -->
            <div class="client-detail-item">
              <div class="detail-label">Fecha de registro:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-11">
                  <i class="bi bi-calendar-check"></i>
                </div>
                <span>{{ client.registrationDate | date:'dd/MM/yyyy HH:mm' }}</span>
              </div>
            </div>

            <!-- Usuarios permitidos -->
            <div class="client-detail-item">
              <div class="detail-label">Usuarios permitidos:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-12">
                  <i class="bi bi-people"></i>
                </div>
                <span>{{ client.allowedUsers }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Estado -->
      <div class="client-status-section">
        <div class="client-detail-item">
          <div class="detail-label">Estado:</div>
          <div class="detail-value">
            <span [class]="client.isActive ? 'status-badge status-active' : 'status-badge status-inactive'">
              {{ client.isActive ? "Activo" : "Inactivo" }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  
  <ng-template pTemplate="footer">
    <div class="dialog-footer">
      <button class="close-button" (click)="hideDialog()">
        <i class="bi bi-x"></i>
        Cerrar
      </button>
      <button class="edit-button" pTooltip="Editar cliente" tooltipPosition="top" (click)="editClient()">
        <i class="bi bi-pencil"></i>
        Editar
      </button>
    </div>
  </ng-template>
</p-dialog>