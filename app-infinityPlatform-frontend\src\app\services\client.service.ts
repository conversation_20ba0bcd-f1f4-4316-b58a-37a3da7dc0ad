import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, tap } from 'rxjs';
import { RestResponse } from '../dtos/restResponse';

@Injectable({
  providedIn: 'root'
})
export class ClientService {
  private baseUrl = 'http://localhost:7084/api/v1/infinity-platform-customers/api/clients';

  constructor(private http: HttpClient) { }

  getAllClients(isActive?: boolean): Observable<RestResponse> {
    let url = this.baseUrl;
    if (isActive !== undefined) {
      url += `?isActive=${isActive}`;
    }
    return this.http.get<RestResponse>(url)
      .pipe(
        tap(response => console.log('getAllClients response:', response))
      );
  }

  getClientById(id: number): Observable<RestResponse> {
    console.log(`Llamando a getClientById con ID: ${id}`);
    return this.http.get<RestResponse>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(response => console.log(`getClientById(${id}) response:`, response))
      );
  }

  searchClientsByCompanyName(companyName: string): Observable<RestResponse> {
    return this.http.get<RestResponse>(`${this.baseUrl}/search?companyName=${companyName}`);
  }

  createClient(client: any): Observable<RestResponse> {
    return this.http.post<RestResponse>(this.baseUrl, client);
  }

  updateClient(id: number, client: any): Observable<RestResponse> {
    return this.http.put<RestResponse>(`${this.baseUrl}/${id}`, client);
  }

  changeClientStatus(id: number, active: boolean): Observable<RestResponse> {
    return this.http.patch<RestResponse>(`${this.baseUrl}/${id}/status?active=${active}`, {});
  }

  deleteClient(id: number): Observable<RestResponse> {
    return this.http.delete<RestResponse>(`${this.baseUrl}/${id}`);
  }
}

