{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-ripple.mjs"], "sourcesContent": ["import { isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, inject, NgZone, effect, Directive, NgModule } from '@angular/core';\nimport { removeClass, getHeight, getWidth, getOuterWidth, getOuterHeight, getOffset, addClass, remove } from '@primeuix/utils';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst theme = ({\n  dt\n}) => `\n/* For PrimeNG */\n.p-ripple {\n    overflow: hidden;\n    position: relative;\n}\n\n.p-ink {\n    display: block;\n    position: absolute;\n    background: ${dt('ripple.background')};\n    border-radius: 100%;\n    transform: scale(0);\n}\n\n.p-ink-active {\n    animation: ripple 0.4s linear;\n}\n\n.p-ripple-disabled .p-ink {\n    display: none !important;\n}\n\n@keyframes ripple {\n    100% {\n        opacity: 0;\n        transform: scale(2.5);\n    }\n}\n`;\nconst classes = {\n  root: 'p-ink'\n};\nclass RippleStyle extends BaseStyle {\n  name = 'ripple';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵRippleStyle_BaseFactory;\n    return function RippleStyle_Factory(__ngFactoryType__) {\n      return (ɵRippleStyle_BaseFactory || (ɵRippleStyle_BaseFactory = i0.ɵɵgetInheritedFactory(RippleStyle)))(__ngFactoryType__ || RippleStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RippleStyle,\n    factory: RippleStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Ripple directive adds ripple effect to the host element.\n *\n * [Live Demo](https://www.primeng.org/ripple)\n *\n * @module ripplestyle\n *\n */\nvar RippleClasses;\n(function (RippleClasses) {\n  /**\n   * Class name of the root element\n   */\n  RippleClasses[\"root\"] = \"p-ink\";\n})(RippleClasses || (RippleClasses = {}));\n\n/**\n * Ripple directive adds ripple effect to the host element.\n * @group Components\n */\nclass Ripple extends BaseComponent {\n  zone = inject(NgZone);\n  _componentStyle = inject(RippleStyle);\n  animationListener;\n  mouseDownListener;\n  timeout;\n  constructor() {\n    super();\n    effect(() => {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.config.ripple()) {\n          this.zone.runOutsideAngular(() => {\n            this.create();\n            this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n          });\n        } else {\n          this.remove();\n        }\n      }\n    });\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n  }\n  onMouseDown(event) {\n    let ink = this.getInk();\n    if (!ink || this.document.defaultView?.getComputedStyle(ink, null).display === 'none') {\n      return;\n    }\n    removeClass(ink, 'p-ink-active');\n    if (!getHeight(ink) && !getWidth(ink)) {\n      let d = Math.max(getOuterWidth(this.el.nativeElement), getOuterHeight(this.el.nativeElement));\n      ink.style.height = d + 'px';\n      ink.style.width = d + 'px';\n    }\n    let offset = getOffset(this.el.nativeElement);\n    let x = event.pageX - offset.left + this.document.body.scrollTop - getWidth(ink) / 2;\n    let y = event.pageY - offset.top + this.document.body.scrollLeft - getHeight(ink) / 2;\n    this.renderer.setStyle(ink, 'top', y + 'px');\n    this.renderer.setStyle(ink, 'left', x + 'px');\n    addClass(ink, 'p-ink-active');\n    this.timeout = setTimeout(() => {\n      let ink = this.getInk();\n      if (ink) {\n        removeClass(ink, 'p-ink-active');\n      }\n    }, 401);\n  }\n  getInk() {\n    const children = this.el.nativeElement.children;\n    for (let i = 0; i < children.length; i++) {\n      if (typeof children[i].className === 'string' && children[i].className.indexOf('p-ink') !== -1) {\n        return children[i];\n      }\n    }\n    return null;\n  }\n  resetInk() {\n    let ink = this.getInk();\n    if (ink) {\n      removeClass(ink, 'p-ink-active');\n    }\n  }\n  onAnimationEnd(event) {\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n    }\n    removeClass(event.currentTarget, 'p-ink-active');\n  }\n  create() {\n    let ink = this.renderer.createElement('span');\n    this.renderer.addClass(ink, 'p-ink');\n    this.renderer.appendChild(this.el.nativeElement, ink);\n    this.renderer.setAttribute(ink, 'aria-hidden', 'true');\n    this.renderer.setAttribute(ink, 'role', 'presentation');\n    if (!this.animationListener) {\n      this.animationListener = this.renderer.listen(ink, 'animationend', this.onAnimationEnd.bind(this));\n    }\n  }\n  remove() {\n    let ink = this.getInk();\n    if (ink) {\n      this.mouseDownListener && this.mouseDownListener();\n      this.animationListener && this.animationListener();\n      this.mouseDownListener = null;\n      this.animationListener = null;\n      remove(ink);\n    }\n  }\n  ngOnDestroy() {\n    if (this.config && this.config.ripple()) {\n      this.remove();\n    }\n    super.ngOnDestroy();\n  }\n  static ɵfac = function Ripple_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Ripple)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: Ripple,\n    selectors: [[\"\", \"pRipple\", \"\"]],\n    hostAttrs: [1, \"p-ripple\"],\n    features: [i0.ɵɵProvidersFeature([RippleStyle]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Ripple, [{\n    type: Directive,\n    args: [{\n      selector: '[pRipple]',\n      host: {\n        class: 'p-ripple'\n      },\n      standalone: true,\n      providers: [RippleStyle]\n    }]\n  }], () => [], null);\n})();\nclass RippleModule {\n  static ɵfac = function RippleModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RippleModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RippleModule,\n    imports: [Ripple],\n    exports: [Ripple]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Ripple],\n      exports: [Ripple]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleClasses, RippleModule, RippleStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAUY,GAAG,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBzC,IAAM,UAAU;AAAA,EACd,MAAM;AACR;AACA,IAAM,cAAN,MAAM,qBAAoB,UAAU;AAAA,EAClC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,aAAY;AAAA,EACvB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,gBAAe;AAIxB,EAAAA,eAAc,MAAM,IAAI;AAC1B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAMxC,IAAM,SAAN,MAAM,gBAAe,cAAc;AAAA,EACjC,OAAO,OAAO,MAAM;AAAA,EACpB,kBAAkB,OAAO,WAAW;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM;AACN,WAAO,MAAM;AACX,UAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,YAAI,KAAK,OAAO,OAAO,GAAG;AACxB,eAAK,KAAK,kBAAkB,MAAM;AAChC,iBAAK,OAAO;AACZ,iBAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,GAAG,eAAe,aAAa,KAAK,YAAY,KAAK,IAAI,CAAC;AAAA,UAC/G,CAAC;AAAA,QACH,OAAO;AACL,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,UAAM,gBAAgB;AAAA,EACxB;AAAA,EACA,YAAY,OAAO;AACjB,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,CAAC,OAAO,KAAK,SAAS,aAAa,iBAAiB,KAAK,IAAI,EAAE,YAAY,QAAQ;AACrF;AAAA,IACF;AACA,gBAAY,KAAK,cAAc;AAC/B,QAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,GAAG,GAAG;AACrC,UAAI,IAAI,KAAK,IAAI,cAAc,KAAK,GAAG,aAAa,GAAG,eAAe,KAAK,GAAG,aAAa,CAAC;AAC5F,UAAI,MAAM,SAAS,IAAI;AACvB,UAAI,MAAM,QAAQ,IAAI;AAAA,IACxB;AACA,QAAI,SAAS,UAAU,KAAK,GAAG,aAAa;AAC5C,QAAI,IAAI,MAAM,QAAQ,OAAO,OAAO,KAAK,SAAS,KAAK,YAAY,SAAS,GAAG,IAAI;AACnF,QAAI,IAAI,MAAM,QAAQ,OAAO,MAAM,KAAK,SAAS,KAAK,aAAa,UAAU,GAAG,IAAI;AACpF,SAAK,SAAS,SAAS,KAAK,OAAO,IAAI,IAAI;AAC3C,SAAK,SAAS,SAAS,KAAK,QAAQ,IAAI,IAAI;AAC5C,aAAS,KAAK,cAAc;AAC5B,SAAK,UAAU,WAAW,MAAM;AAC9B,UAAIC,OAAM,KAAK,OAAO;AACtB,UAAIA,MAAK;AACP,oBAAYA,MAAK,cAAc;AAAA,MACjC;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAAA,EACA,SAAS;AACP,UAAM,WAAW,KAAK,GAAG,cAAc;AACvC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAI,OAAO,SAAS,CAAC,EAAE,cAAc,YAAY,SAAS,CAAC,EAAE,UAAU,QAAQ,OAAO,MAAM,IAAI;AAC9F,eAAO,SAAS,CAAC;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,KAAK;AACP,kBAAY,KAAK,cAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,SAAS;AAChB,mBAAa,KAAK,OAAO;AAAA,IAC3B;AACA,gBAAY,MAAM,eAAe,cAAc;AAAA,EACjD;AAAA,EACA,SAAS;AACP,QAAI,MAAM,KAAK,SAAS,cAAc,MAAM;AAC5C,SAAK,SAAS,SAAS,KAAK,OAAO;AACnC,SAAK,SAAS,YAAY,KAAK,GAAG,eAAe,GAAG;AACpD,SAAK,SAAS,aAAa,KAAK,eAAe,MAAM;AACrD,SAAK,SAAS,aAAa,KAAK,QAAQ,cAAc;AACtD,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,oBAAoB,KAAK,SAAS,OAAO,KAAK,gBAAgB,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,IACnG;AAAA,EACF;AAAA,EACA,SAAS;AACP,QAAI,MAAM,KAAK,OAAO;AACtB,QAAI,KAAK;AACP,WAAK,qBAAqB,KAAK,kBAAkB;AACjD,WAAK,qBAAqB,KAAK,kBAAkB;AACjD,WAAK,oBAAoB;AACzB,WAAK,oBAAoB;AACzB,aAAO,GAAG;AAAA,IACZ;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,UAAU,KAAK,OAAO,OAAO,GAAG;AACvC,WAAK,OAAO;AAAA,IACd;AACA,UAAM,YAAY;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAQ;AAAA,EAC3C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,IAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,IACzB,UAAU,CAAI,mBAAmB,CAAC,WAAW,CAAC,GAAM,0BAA0B;AAAA,EAChF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,MACZ,WAAW,CAAC,WAAW;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,MAAM;AAAA,IAChB,SAAS,CAAC,MAAM;AAAA,EAClB,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,MAAM;AAAA,MAChB,SAAS,CAAC,MAAM;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["RippleClasses", "ink"]}