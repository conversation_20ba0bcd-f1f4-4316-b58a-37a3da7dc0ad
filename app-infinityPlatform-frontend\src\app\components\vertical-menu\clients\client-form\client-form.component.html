<div class="client-form-container">
  <div class="content-wrapper">
    <!-- Header -->
    <div class="form-header">
      <div class="header-content">
        <div class="header-left">
          <button
            class="back-button"
            (click)="navigateBack()"
            pTooltip="Volver a la lista de clientes"
            tooltipPosition="bottom">
            <i class="bi bi-arrow-left"></i>
            Volver
          </button>
          <div class="header-text">
            <h1>{{ isEditMode ? 'Editar Cliente' : 'Crear Nuevo Cliente' }}</h1>
            <p>{{ isEditMode ? 'Modifique los datos del cliente' : 'Complete la información del nuevo cliente' }}</p>
          </div>
        </div>
      </div>
    </div>

  <!-- Form Container -->
  <div class="form-container">
    <form [formGroup]="clientForm" (ngSubmit)="saveClient()">
      
      <!-- Información básica -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="bi bi-building"></i>
          Información Básica
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="companyName">Nombre de la Empresa *</label>
            <input 
              id="companyName" 
              type="text" 
              pInputText 
              formControlName="companyName" 
              placeholder="Ingrese el nombre de la empresa"
              [class.ng-invalid]="clientForm.get('companyName')?.invalid && clientForm.get('companyName')?.touched">
            <small 
              *ngIf="clientForm.get('companyName')?.invalid && clientForm.get('companyName')?.touched" 
              class="error-message">
              El nombre de la empresa es requerido
            </small>
          </div>

          <div class="form-group">
            <label for="businessName">Razón Social *</label>
            <input 
              id="businessName" 
              type="text" 
              pInputText 
              formControlName="businessName" 
              placeholder="Ingrese la razón social"
              [class.ng-invalid]="clientForm.get('businessName')?.invalid && clientForm.get('businessName')?.touched">
            <small 
              *ngIf="clientForm.get('businessName')?.invalid && clientForm.get('businessName')?.touched" 
              class="error-message">
              La razón social es requerida
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="taxId">NIT *</label>
            <input 
              id="taxId" 
              type="text" 
              pInputText 
              formControlName="taxId" 
              placeholder="Ingrese el NIT"
              [class.ng-invalid]="clientForm.get('taxId')?.invalid && clientForm.get('taxId')?.touched">
            <small 
              *ngIf="clientForm.get('taxId')?.invalid && clientForm.get('taxId')?.touched" 
              class="error-message">
              El NIT es requerido
            </small>
          </div>

          <div class="form-group">
            <label for="verificationNumber">Dígito de Verificación *</label>
            <input 
              id="verificationNumber" 
              type="text" 
              pInputText 
              formControlName="verificationNumber" 
              placeholder="Ingrese el dígito de verificación"
              [class.ng-invalid]="clientForm.get('verificationNumber')?.invalid && clientForm.get('verificationNumber')?.touched">
            <small 
              *ngIf="clientForm.get('verificationNumber')?.invalid && clientForm.get('verificationNumber')?.touched" 
              class="error-message">
              El dígito de verificación es requerido
            </small>
          </div>
        </div>
      </div>

      <!-- Información de contacto -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="bi bi-person-lines-fill"></i>
          Información de Contacto
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="contactName">Nombre del Contacto *</label>
            <input 
              id="contactName" 
              type="text" 
              pInputText 
              formControlName="contactName" 
              placeholder="Ingrese el nombre del contacto"
              [class.ng-invalid]="clientForm.get('contactName')?.invalid && clientForm.get('contactName')?.touched">
            <small 
              *ngIf="clientForm.get('contactName')?.invalid && clientForm.get('contactName')?.touched" 
              class="error-message">
              El nombre del contacto es requerido
            </small>
          </div>

          <div class="form-group">
            <label for="email">Email *</label>
            <input 
              id="email" 
              type="email" 
              pInputText 
              formControlName="email" 
              placeholder="Ingrese el email"
              [class.ng-invalid]="clientForm.get('email')?.invalid && clientForm.get('email')?.touched">
            <small 
              *ngIf="clientForm.get('email')?.invalid && clientForm.get('email')?.touched" 
              class="error-message">
              <span *ngIf="clientForm.get('email')?.errors?.['required']">El email es requerido</span>
              <span *ngIf="clientForm.get('email')?.errors?.['email']">Ingrese un email válido</span>
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="countryCode">Código de País *</label>
            <input 
              id="countryCode" 
              type="text" 
              pInputText 
              formControlName="countryCode" 
              placeholder="Ej: +57"
              [class.ng-invalid]="clientForm.get('countryCode')?.invalid && clientForm.get('countryCode')?.touched">
            <small 
              *ngIf="clientForm.get('countryCode')?.invalid && clientForm.get('countryCode')?.touched" 
              class="error-message">
              El código de país es requerido
            </small>
          </div>

          <div class="form-group">
            <label for="phone">Teléfono *</label>
            <input 
              id="phone" 
              type="text" 
              pInputText 
              formControlName="phone" 
              placeholder="Ingrese el número de teléfono"
              [class.ng-invalid]="clientForm.get('phone')?.invalid && clientForm.get('phone')?.touched">
            <small 
              *ngIf="clientForm.get('phone')?.invalid && clientForm.get('phone')?.touched" 
              class="error-message">
              El teléfono es requerido
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group full-width">
            <label for="address">Dirección *</label>
            <input 
              id="address" 
              type="text" 
              pInputText 
              formControlName="address" 
              placeholder="Ingrese la dirección completa"
              [class.ng-invalid]="clientForm.get('address')?.invalid && clientForm.get('address')?.touched">
            <small 
              *ngIf="clientForm.get('address')?.invalid && clientForm.get('address')?.touched" 
              class="error-message">
              La dirección es requerida
            </small>
          </div>
        </div>
      </div>

      <!-- Información fiscal y adicional -->
      <div class="form-section">
        <h3 class="section-title">
          <i class="bi bi-calculator"></i>
          Información Fiscal y Adicional
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="birthDate">Fecha de Nacimiento *</label>
            <p-calendar 
              id="birthDate"
              formControlName="birthDate"
              [showIcon]="true"
              dateFormat="dd/mm/yy"
              placeholder="Seleccione la fecha"
              [class.ng-invalid]="clientForm.get('birthDate')?.invalid && clientForm.get('birthDate')?.touched">
            </p-calendar>
            <small 
              *ngIf="clientForm.get('birthDate')?.invalid && clientForm.get('birthDate')?.touched" 
              class="error-message">
              La fecha de nacimiento es requerida
            </small>
          </div>

          <div class="form-group">
            <label for="allowedUsers">Usuarios Permitidos *</label>
            <p-inputNumber 
              id="allowedUsers"
              formControlName="allowedUsers"
              [min]="1"
              [max]="1000"
              placeholder="Número de usuarios"
              [class.ng-invalid]="clientForm.get('allowedUsers')?.invalid && clientForm.get('allowedUsers')?.touched">
            </p-inputNumber>
            <small 
              *ngIf="clientForm.get('allowedUsers')?.invalid && clientForm.get('allowedUsers')?.touched" 
              class="error-message">
              El número de usuarios permitidos es requerido (mínimo 1)
            </small>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="vatRate">Tasa de IVA (%) *</label>
            <p-inputNumber 
              id="vatRate"
              formControlName="vatRate"
              [min]="0"
              [max]="100"
              [minFractionDigits]="2"
              [maxFractionDigits]="2"
              placeholder="0.00"
              [class.ng-invalid]="clientForm.get('vatRate')?.invalid && clientForm.get('vatRate')?.touched">
            </p-inputNumber>
            <small 
              *ngIf="clientForm.get('vatRate')?.invalid && clientForm.get('vatRate')?.touched" 
              class="error-message">
              La tasa de IVA es requerida (0-100%)
            </small>
          </div>

          <div class="form-group">
            <label for="consumptionTaxRate">Tasa de Impuesto al Consumo (%) *</label>
            <p-inputNumber 
              id="consumptionTaxRate"
              formControlName="consumptionTaxRate"
              [min]="0"
              [max]="100"
              [minFractionDigits]="2"
              [maxFractionDigits]="2"
              placeholder="0.00"
              [class.ng-invalid]="clientForm.get('consumptionTaxRate')?.invalid && clientForm.get('consumptionTaxRate')?.touched">
            </p-inputNumber>
            <small 
              *ngIf="clientForm.get('consumptionTaxRate')?.invalid && clientForm.get('consumptionTaxRate')?.touched" 
              class="error-message">
              La tasa de impuesto al consumo es requerida (0-100%)
            </small>
          </div>
        </div>

        <!-- Usuario actual (solo lectura) -->
        <div class="form-row">
          <div class="form-group">
            <label for="currentUser">Usuario Creador</label>
            <input 
              id="currentUser" 
              type="text" 
              pInputText 
              [value]="currentUserName" 
              readonly
              class="readonly-field">
            <small class="info-message">
              Este campo se establece automáticamente con el usuario logueado
            </small>
          </div>

          <!-- Estado (solo en modo edición) -->
          <div class="form-group" *ngIf="isEditMode">
            <label for="isActive">Estado</label>
            <div class="checkbox-container">
              <input 
                id="isActive" 
                type="checkbox" 
                formControlName="isActive">
              <label for="isActive" class="checkbox-label">Cliente activo</label>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button 
          type="button" 
          class="cancel-button" 
          (click)="navigateBack()"
          [disabled]="isLoading">
          <i class="bi bi-x-circle"></i>
          Cancelar
        </button>
        
        <button 
          type="submit" 
          class="save-button" 
          [disabled]="isLoading || clientForm.invalid">
          <i class="bi bi-check-circle" *ngIf="!isLoading"></i>
          <i class="bi bi-arrow-clockwise spin" *ngIf="isLoading"></i>
          {{ isLoading ? 'Guardando...' : (isEditMode ? 'Actualizar Cliente' : 'Crear Cliente') }}
        </button>
      </div>
    </form>
  </div>
  </div>
</div>

<!-- Toast Messages -->
<p-toast></p-toast>

<!-- Generic Dialog Component -->
<app-generic-dialog></app-generic-dialog>
