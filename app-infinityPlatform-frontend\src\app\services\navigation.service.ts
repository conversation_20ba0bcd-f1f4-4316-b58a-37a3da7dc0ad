import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class NavigationService {
  constructor(private navigateTo: Router) {}

  gotoUsers() {
    this.navigateTo.navigate(['main-cloud/users']);
  }

  gotoCreateUpdateUser(userId?: number): void {
    if (userId) {
      this.navigateTo.navigate(['main-cloud/users/edit', userId]);
    } else {
      this.navigateTo.navigate(['main-cloud/users/create']);
    }
  }

  gotoProducts() {
    this.navigateTo.navigate(['main-cloud/products']);
  }

  gotoCreateUpdateProduct() {
    this.navigateTo.navigate(['main-cloud/products/create']);
  }

  gotoDashboard() {
    this.navigateTo.navigate(['main-cloud/dashboard']);
  }

  gotoReports() {
    this.navigateTo.navigate(['main-cloud/reports']);
  }

  gotoOrders() {
    this.navigateTo.navigate(['main-cloud/orders']);
  }

  gotoOrdersSummary() {
    this.navigateTo.navigate(['main-cloud/orders/summary']);
  }

  gotoAddProductToOrder() {
    this.navigateTo.navigate(['main-cloud/orders/summary/add-product']);
  }

  gotoAvailability() {
    this.navigateTo.navigate(['main-cloud/availability']);
  }
  gotoClients() {
    this.navigateTo.navigate(['main-cloud/clients']);
  }

  gotoCreateClient(): void {
    this.navigateTo.navigate(['main-cloud/clients/create']);
  }

  gotoCreateUpdateClient(clientId?: number): void {
    if (clientId) {
      this.navigateTo.navigate(['main-cloud/clients/edit', clientId]);
    } else {
      this.navigateTo.navigate(['main-cloud/clients/create']);
    }
  }
  gotoSuppliers() {
    this.navigateTo.navigate(['main-cloud/suppliers']);
  }
  gotoQuickLinks() {
    this.navigateTo.navigate(['main-cloud/quick-links']);
  }

  gotoMainMenu() {
    this.navigateTo.navigate(['main-cloud/quick-links']);
  }
  gotoInvoices() {
    this.navigateTo.navigate(['main-cloud/invoices']);
  }
  gotoExpenses() {
    this.navigateTo.navigate(['main-cloud/expenses']);
  }
  gotoCreateUpdateExpense(expenseId?: number): void {
    if (expenseId) {
      this.navigateTo.navigate(['main-cloud/expenses/edit', expenseId]);
    } else {
      this.navigateTo.navigate(['main-cloud/expenses/create']);
    }
  }
  gotoManage() {
    this.navigateTo.navigate(['main-cloud/manage']);
  }

  gotoIncomes() {
    this.navigateTo.navigate(['main-cloud/incomes']);
  }

  gotoCreateUpdateIncome(incomeId?: number): void {
    if (incomeId) {
      this.navigateTo.navigate(['main-cloud/incomes/edit', incomeId]);
    } else {
      this.navigateTo.navigate(['main-cloud/incomes/create']);
    }
  }

  gotoSubscriptions(): void {
    console.log('NavigationService: Navegando a subscriptions');
    this.navigateTo.navigate(['main-cloud/subscriptions']);
  }

  // Método para navegar a la lista de incidencias
  gotoIncidents(): void {
    this.navigateTo.navigate(['main-cloud/incidents']);
  }

  // Método para navegar a la creación/actualización de incidencias
  gotoCreateUpdateIncident(incidentId?: number): void {
    if (incidentId) {
      this.navigateTo.navigate(['main-cloud/incidents/edit', incidentId]);
    } else {
      this.navigateTo.navigate(['main-cloud/incidents/create']);
    }
  }
}
