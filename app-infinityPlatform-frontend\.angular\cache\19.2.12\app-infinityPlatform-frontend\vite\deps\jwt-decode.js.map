{"version": 3, "sources": ["../../../../../../node_modules/jwt-decode/build/esm/index.js"], "sourcesContent": ["export class InvalidTokenError extends Error {}\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\nfunction b64DecodeUnicode(str) {\n  return decodeURIComponent(atob(str).replace(/(.)/g, (m, p) => {\n    let code = p.charCodeAt(0).toString(16).toUpperCase();\n    if (code.length < 2) {\n      code = \"0\" + code;\n    }\n    return \"%\" + code;\n  }));\n}\nfunction base64UrlDecode(str) {\n  let output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  switch (output.length % 4) {\n    case 0:\n      break;\n    case 2:\n      output += \"==\";\n      break;\n    case 3:\n      output += \"=\";\n      break;\n    default:\n      throw new Error(\"base64 string is not of the correct length\");\n  }\n  try {\n    return b64DecodeUnicode(output);\n  } catch (err) {\n    return atob(output);\n  }\n}\nexport function jwtDecode(token, options) {\n  if (typeof token !== \"string\") {\n    throw new InvalidTokenError(\"Invalid token specified: must be a string\");\n  }\n  options || (options = {});\n  const pos = options.header === true ? 0 : 1;\n  const part = token.split(\".\")[pos];\n  if (typeof part !== \"string\") {\n    throw new InvalidTokenError(`Invalid token specified: missing part #${pos + 1}`);\n  }\n  let decoded;\n  try {\n    decoded = base64UrlDecode(part);\n  } catch (e) {\n    throw new InvalidTokenError(`Invalid token specified: invalid base64 for part #${pos + 1} (${e.message})`);\n  }\n  try {\n    return JSON.parse(decoded);\n  } catch (e) {\n    throw new InvalidTokenError(`Invalid token specified: invalid json for part #${pos + 1} (${e.message})`);\n  }\n}"], "mappings": ";;;AAAO,IAAM,oBAAN,cAAgC,MAAM;AAAC;AAC9C,kBAAkB,UAAU,OAAO;AACnC,SAAS,iBAAiB,KAAK;AAC7B,SAAO,mBAAmB,KAAK,GAAG,EAAE,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAC5D,QAAI,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AACpD,QAAI,KAAK,SAAS,GAAG;AACnB,aAAO,MAAM;AAAA,IACf;AACA,WAAO,MAAM;AAAA,EACf,CAAC,CAAC;AACJ;AACA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,SAAS,IAAI,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG;AACrD,UAAQ,OAAO,SAAS,GAAG;AAAA,IACzB,KAAK;AACH;AAAA,IACF,KAAK;AACH,gBAAU;AACV;AAAA,IACF,KAAK;AACH,gBAAU;AACV;AAAA,IACF;AACE,YAAM,IAAI,MAAM,4CAA4C;AAAA,EAChE;AACA,MAAI;AACF,WAAO,iBAAiB,MAAM;AAAA,EAChC,SAAS,KAAK;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AACO,SAAS,UAAU,OAAO,SAAS;AACxC,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,kBAAkB,2CAA2C;AAAA,EACzE;AACA,cAAY,UAAU,CAAC;AACvB,QAAM,MAAM,QAAQ,WAAW,OAAO,IAAI;AAC1C,QAAM,OAAO,MAAM,MAAM,GAAG,EAAE,GAAG;AACjC,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,IAAI,kBAAkB,0CAA0C,MAAM,CAAC,EAAE;AAAA,EACjF;AACA,MAAI;AACJ,MAAI;AACF,cAAU,gBAAgB,IAAI;AAAA,EAChC,SAAS,GAAG;AACV,UAAM,IAAI,kBAAkB,qDAAqD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EAC3G;AACA,MAAI;AACF,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B,SAAS,GAAG;AACV,UAAM,IAAI,kBAAkB,mDAAmD,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG;AAAA,EACzG;AACF;", "names": []}