:host {
  --primary-color: #1d3c34; /* Verde oscuro */
  --primary-hover: #0f2a23; /* Verde más oscuro */
  --primary-light: #e8f0ed; /* Verde claro */
  --text-color: #2c2c2c; /* Casi negro */
  --text-light: #5f5f5f; /* Gris oscuro */
  --text-lighter: #8a8a8a; /* Gris medio */
  --background: #f5f5f0; /* Beige claro */
  --background-card: #ffffff; /* Blanco */
  --background-hover: #f0efe6; /* Beige más claro */
  --border-color: #d8d3c5; /* Beige medio */
  --accent-color: #b8860b; /* Dorado oscuro */
  --accent-light: #d4af37; /* Dorado */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;
  --transition: all 0.3s ease;
  --success-color: #198754;
  --danger-color: #dc3545;
  --info-color: #0dcaf0;
  --warning-color: #ffc107;

  display: block;
  min-height: 100vh;
  background-color: var(--background);
  padding: 1.5rem;
  font-family: "Playfair Display", Georgia, serif;
}

/* ===== LAYOUT PRINCIPAL ===== */

.client-form-container {
  max-width: 1280px;
  margin: 0 auto;
}

.content-wrapper {
  background-color: var(--background-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* ===== HEADER SECTION ===== */

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.75rem;
  background: linear-gradient(to right, rgba(29, 60, 52, 0.02), rgba(29, 60, 52, 0.05));
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-grow: 1;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

.back-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius);
}

.back-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.back-button:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button i {
  font-size: 1.25rem;
}

.header-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.header-text h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-color);
  letter-spacing: 0.3px;
}

.header-text p {
  margin: 0;
  font-size: 1rem;
  color: var(--text-light);
  font-weight: 400;
}

/* ===== FORM CONTAINER ===== */

.form-container {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* ===== FORM SECTIONS ===== */

.form-section {
  margin-bottom: 2.5rem;
  padding: 2rem;
  background: var(--background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.5rem 0;
  color: var(--text-color);
  font-size: 1.3rem;
  font-weight: 700;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
  letter-spacing: 0.3px;
}

.section-title i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius);
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: white;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ===== FORM LAYOUT ===== */

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

/* ===== LABELS ===== */

label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-color);
  font-family: "Playfair Display", Georgia, serif;
  letter-spacing: 0.3px;
  font-size: 1.05rem;
}

/* ===== INPUT STYLES ===== */

:host ::ng-deep .p-inputtext,
:host ::ng-deep .p-inputnumber-input {
  width: 100%;
  padding: 1rem;
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
  background-color: var(--background);
  font-size: 1rem;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-family: "Playfair Display", Georgia, serif;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

:host ::ng-deep .p-inputtext:focus,
:host ::ng-deep .p-inputnumber-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(29, 60, 52, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0);
  background-color: var(--background-card);
}

:host ::ng-deep .p-inputtext::placeholder {
  color: var(--text-lighter);
  font-family: "Playfair Display", Georgia, serif;
}

input.readonly-field {
  background-color: var(--primary-light);
  color: var(--text-light);
  cursor: not-allowed;
  border-color: var(--border-color);
}

/* ===== INPUT NUMBER ===== */

:host ::ng-deep .p-inputnumber {
  width: 100%;
}

:host ::ng-deep .p-inputnumber-input {
  width: 100%;
  text-align: left;
}

:host ::ng-deep .p-inputnumber-button {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--border-color);
}

:host ::ng-deep .p-inputnumber-button:hover {
  background-color: var(--primary-color);
  color: white;
}

/* ===== CALENDAR STYLES ===== */

:host ::ng-deep .p-calendar {
  width: 100%;
}

:host ::ng-deep .p-calendar .p-inputtext {
  width: 100%;
  background: var(--background);
  color: var(--text-color);
}

:host ::ng-deep .p-calendar .p-datepicker {
  background: var(--background-card);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border-radius: var(--radius) var(--radius) 0 0;
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td > span {
  color: var(--text-color);
  font-family: "Playfair Display", Georgia, serif;
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td > span:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td > span.p-highlight {
  background: var(--accent-color);
  color: white;
}

/* ===== CHECKBOX STYLES ===== */

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: var(--primary-light);
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.checkbox-label {
  margin: 0;
  font-weight: 500;
  cursor: pointer;
  color: var(--text-color);
  font-family: "Playfair Display", Georgia, serif;
}

input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
  accent-color: var(--primary-color);
}

/* ===== ERROR AND INFO MESSAGES ===== */

.error-message {
  color: var(--danger-color);
  display: block;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 500;
}

.info-message {
  color: var(--text-lighter);
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
  font-family: "Playfair Display", Georgia, serif;
  font-style: italic;
}

/* ===== INVALID INPUT STYLES ===== */

.ng-invalid.ng-touched {
  border-color: var(--danger-color) !important;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}

:host ::ng-deep .p-inputtext.ng-invalid.ng-touched,
:host ::ng-deep .p-inputnumber-input.ng-invalid.ng-touched {
  border-color: var(--danger-color) !important;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}

/* ===== FORM ACTIONS ===== */

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1.25rem;
  margin-top: 2.5rem;
  padding: 2rem;
  background: var(--background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* ===== BUTTON STYLES ===== */

.cancel-button,
.save-button {
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 600;
  padding: 0.875rem 1.75rem;
  border-radius: var(--radius);
  transition: var(--transition);
  font-size: 1rem;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 160px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.cancel-button {
  background: linear-gradient(135deg, var(--text-light), var(--text-lighter));
  color: white;
}

.cancel-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius);
}

.cancel-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--text-lighter), var(--text-light));
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.save-button {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: white;
}

.save-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius);
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--accent-light), var(--accent-color));
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.save-button:active:not(:disabled),
.cancel-button:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.save-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: var(--shadow-sm) !important;
}

/* ===== SPIN ANIMATION ===== */

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== FOCUS IMPROVEMENTS ===== */

.back-button:focus,
:host ::ng-deep .p-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

:host ::ng-deep .p-inputtext:focus,
:host ::ng-deep .p-inputnumber-input:focus,
:host ::ng-deep .p-calendar .p-inputtext:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(29, 60, 52, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 992px) {
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .form-container {
    padding: 1.5rem;
  }

  .header-left {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  :host {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-container {
    padding: 1rem;
  }

  .form-section {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
    padding: 1.5rem;
    gap: 1rem;
  }

  .cancel-button,
  .save-button {
    width: 100%;
    min-width: auto;
  }

  .header-text h1 {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .form-section {
    padding: 1rem;
  }

  .form-actions {
    padding: 1rem;
  }

  .header-text h1 {
    font-size: 1.3rem;
  }

  .back-button {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* ===== LOADING STATES ===== */

:host ::ng-deep .p-button[loading="true"] {
  opacity: 0.7;
  cursor: not-allowed;
}

/* ===== PRINT STYLES ===== */

@media print {
  :host {
    background: white;
    padding: 0;
  }

  .back-button,
  .form-actions {
    display: none;
  }

  .content-wrapper,
  .form-section {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
