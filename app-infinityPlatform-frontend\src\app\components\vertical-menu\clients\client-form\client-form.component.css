/* Variables CSS */
:root {
  --primary-color: #2c3e50;
  --primary-hover: #34495e;
  --secondary-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --background: #f8f9fa;
  --background-card: #ffffff;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #95a5a6;
  --border-color: #e9ecef;
  --border-radius: 8px;
  --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Container principal */
.client-form-container {
  min-height: 100vh;
  background: var(--background);
  font-family: "Playfair Display", Georgia, serif;
}

/* Header */
.form-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: 2rem 0;
  margin-bottom: 2rem;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.75rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-button i {
  font-size: 1.2rem;
}

.header-text h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.header-text p {
  margin: 0.5rem 0 0 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Form Container */
.form-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
}

/* Form Sections */
.form-section {
  background: var(--background-card);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: var(--shadow);
  transition: box-shadow 0.3s ease;
}

.form-section:hover {
  box-shadow: var(--shadow-hover);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0 0 1.5rem 0;
  color: var(--primary-color);
  font-size: 1.3rem;
  font-weight: 600;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
}

.section-title i {
  font-size: 1.2rem;
  color: var(--secondary-color);
}

/* Form Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

/* Labels */
label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

/* Input Styles */
input[type="text"],
input[type="email"],
input[type="number"] {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  color: var(--text-primary);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="number"]:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

input.readonly-field {
  background-color: #f8f9fa;
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* PrimeNG Input Overrides */
:host ::ng-deep .p-inputtext {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  color: var(--text-primary);
  width: 100%;
}

:host ::ng-deep .p-inputtext:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

:host ::ng-deep .p-inputnumber {
  width: 100%;
}

:host ::ng-deep .p-inputnumber .p-inputtext {
  width: 100%;
}

/* Calendar Styles */
:host ::ng-deep .p-calendar {
  width: 100%;
}

:host ::ng-deep .p-calendar .p-inputtext {
  width: 100%;
  background: white;
  color: black;
}

:host ::ng-deep .p-calendar .p-datepicker {
  background: white;
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-header {
  background: var(--primary-color);
  color: white;
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td > span {
  color: var(--text-primary);
}

:host ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td > span:hover {
  background: var(--secondary-color);
  color: white;
}

/* Checkbox Styles */
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-label {
  margin: 0;
  font-weight: normal;
  cursor: pointer;
}

input[type="checkbox"] {
  width: 1.2rem;
  height: 1.2rem;
  cursor: pointer;
}

/* Error and Info Messages */
.error-message {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

.info-message {
  color: var(--text-light);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

/* Invalid Input Styles */
.ng-invalid.ng-touched {
  border-color: var(--danger-color) !important;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem;
  background: var(--background-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-top: 2rem;
}

.cancel-button,
.save-button {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 150px;
  justify-content: center;
}

.cancel-button {
  background: var(--text-light);
  color: white;
}

.cancel-button:hover:not(:disabled) {
  background: var(--text-secondary);
  transform: translateY(-2px);
}

.save-button {
  background: var(--success-color);
  color: white;
}

.save-button:hover:not(:disabled) {
  background: #229954;
  transform: translateY(-2px);
}

.save-button:disabled,
.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Spin Animation */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .header-content,
  .form-container {
    padding: 0 1rem;
  }
  
  .form-section {
    padding: 1.5rem;
  }
  
  .form-actions {
    flex-direction: column;
    padding: 1.5rem;
  }
  
  .cancel-button,
  .save-button {
    width: 100%;
  }
}
