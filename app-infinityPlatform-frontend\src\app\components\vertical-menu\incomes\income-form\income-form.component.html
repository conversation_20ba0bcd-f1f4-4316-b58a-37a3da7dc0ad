<div class="income-form-container">
  <div class="content-wrapper">
    <div class="header-section">
      <div class="title-container">
        <button class="back-button" (click)="navigateBack()">
          <i class="bi bi-arrow-left"></i>
          Volver
        </button>
        <div class="title-icon">
          <i class="bi bi-cash"></i>
        </div>
        <h2 class="page-title">{{ isEditMode ? 'Editar' : 'Crear' }} Ingreso</h2>
      </div>
    </div>

    <div class="form-container">
      <form [formGroup]="incomeForm" (ngSubmit)="saveIncome()">
        <div class="form-group">
          <label for="amount">Monto</label>
          <div class="amount-input-container">
            <p-inputNumber
              id="amount"
              formControlName="amount"
              mode="currency"
              currency="USD"
              locale="en-US"
              [min]="0.01"
              [step]="0.01"
              placeholder="0.00"
              [style]="{ width: '100%' }"
            ></p-inputNumber>
            <button
              type="button"
              class="money-counter-button"
              (click)="openMoneyCounter()"
              pTooltip="Contador de dinero"
              tooltipPosition="top"
            >
              <i class="bi bi-calculator"></i>
            </button>
          </div>
          <small
            class="error-message"
            *ngIf="
              incomeForm.get('amount')?.invalid &&
              incomeForm.get('amount')?.touched
            "
          >
            El monto es requerido y debe ser mayor a 0
          </small>
        </div>

        <div class="form-group">
          <label for="description">Descripción</label>
          <input
            pInputText
            id="description"
            type="text"
            formControlName="description"
            placeholder="Descripción del ingreso"
            [style]="{ width: '100%' }"
          />
          <small
            class="error-message"
            *ngIf="
              incomeForm.get('description')?.invalid &&
              incomeForm.get('description')?.touched
            "
          >
            La descripción es requerida y no debe exceder 255 caracteres
          </small>
        </div>

        <div class="form-group">
          <label for="incomeType">Tipo de Ingreso</label>
          <p-dropdown
            id="incomeType"
            formControlName="incomeTypeId"
            [options]="incomeTypes"
            optionLabel="name"
            optionValue="incomeTypeId"
            placeholder="Seleccione un tipo de ingreso"
            [style]="{ width: '100%' }"
            [filter]="true"
            filterBy="name"
            [showClear]="true"
            [autoDisplayFirst]="false"
            [scrollHeight]="'280px'"
            styleClass="professional-dropdown"
            appendTo="body"
          >
            <!-- Item seleccionado -->
            <ng-template pTemplate="selectedItem" let-incomeType>
              <div class="selected-category-professional" *ngIf="incomeType">
                <div class="category-icon">
                  <i class="bi bi-cash-coin"></i>
                </div>
                <div class="category-info">
                  <span class="category-name">{{ incomeType.name }}</span>
                  <span class="category-type">Tipo de ingreso</span>
                </div>
              </div>
            </ng-template>

            <!-- Item en la lista -->
            <ng-template pTemplate="item" let-incomeType>
              <div class="category-item-professional">
                <div class="category-icon">
                  <i class="bi bi-cash-coin"></i>
                </div>
                <div class="category-content">
                  <div class="category-name">{{ incomeType.name }}</div>
                  <div
                    class="category-description"
                    *ngIf="incomeType.description"
                  >
                    {{ incomeType.description }}
                  </div>
                  <div
                    class="category-description"
                    *ngIf="!incomeType.description"
                  >
                    Sin descripción
                  </div>
                </div>
                <div class="category-arrow">
                  <i class="bi bi-chevron-right"></i>
                </div>
              </div>
            </ng-template>

            <!-- Encabezado -->
            <ng-template pTemplate="header">
              <div class="dropdown-header-professional">
                <div class="header-icon">
                  <i class="bi bi-collection"></i>
                </div>
                <div class="header-content">
                  <span class="header-title">Tipos de Ingresos</span>
                  <span class="header-subtitle"
                    >Seleccione el tipo apropiado</span
                  >
                </div>
              </div>
            </ng-template>

            <!-- Mensaje cuando no hay resultados del filtro -->
            <ng-template pTemplate="emptyfilter">
              <div class="empty-message-professional">
                <div class="empty-icon">
                  <i class="bi bi-search"></i>
                </div>
                <span class="empty-text">No se encontraron tipos</span>
                <span class="empty-subtext"
                  >Intente con otro término de búsqueda</span
                >
              </div>
            </ng-template>

            <!-- Pie -->
            <ng-template pTemplate="footer">
              <div class="dropdown-footer-professional">
                <div class="footer-info">
                  <i class="bi bi-info-circle"></i>
                  <span>{{ incomeTypes.length }} tipos disponibles</span>
                </div>
              </div>
            </ng-template>
          </p-dropdown>

          <small
            class="error-message"
            *ngIf="
              incomeForm.get('incomeTypeId')?.invalid &&
              incomeForm.get('incomeTypeId')?.touched
            "
          >
            El tipo de ingreso es requerido
          </small>
        </div>

        <div class="form-group">
          <label for="client">Cliente</label>
          <p-dropdown
            id="client"
            formControlName="clientId"
            [options]="clients"
            optionLabel="companyName"
            optionValue="clientId"
            placeholder="Seleccione un cliente"
            [style]="{ width: '100%' }"
            [filter]="true"
            filterBy="companyName"
            [showClear]="true"
            [autoDisplayFirst]="false"
            [scrollHeight]="'280px'"
            styleClass="professional-dropdown"
            appendTo="body"
          >
            <!-- Item seleccionado -->
            <ng-template pTemplate="selectedItem" let-client>
              <div class="selected-category-professional" *ngIf="client">
                <div class="category-icon">
                  <i class="bi bi-building"></i>
                </div>
                <div class="category-info">
                  <span class="category-name">{{ client.companyName }}</span>
                  <span class="category-type">Cliente</span>
                </div>
              </div>
            </ng-template>

            <!-- Item en la lista -->
            <ng-template pTemplate="item" let-client>
              <div class="category-item-professional">
                <div class="category-icon">
                  <i class="bi bi-building"></i>
                </div>
                <div class="category-content">
                  <div class="category-name">{{ client.companyName }}</div>
                  <div
                    class="category-description"
                    *ngIf="client.businessName"
                  >
                    {{ client.businessName }}
                  </div>
                  <div
                    class="category-description"
                    *ngIf="!client.businessName"
                  >
                    Sin nombre comercial
                  </div>
                </div>
                <div class="category-arrow">
                  <i class="bi bi-chevron-right"></i>
                </div>
              </div>
            </ng-template>

            <!-- Encabezado -->
            <ng-template pTemplate="header">
              <div class="dropdown-header-professional">
                <div class="header-icon">
                  <i class="bi bi-collection"></i>
                </div>
                <div class="header-content">
                  <span class="header-title">Clientes</span>
                  <span class="header-subtitle"
                    >Seleccione el cliente apropiado</span
                  >
                </div>
              </div>
            </ng-template>

            <!-- Mensaje cuando no hay resultados del filtro -->
            <ng-template pTemplate="emptyfilter">
              <div class="empty-message-professional">
                <div class="empty-icon">
                  <i class="bi bi-search"></i>
                </div>
                <span class="empty-text">No se encontraron clientes</span>
                <span class="empty-subtext"
                  >Intente con otro término de búsqueda</span
                >
              </div>
            </ng-template>

            <!-- Pie -->
            <ng-template pTemplate="footer">
              <div class="dropdown-footer-professional">
                <div class="footer-info">
                  <i class="bi bi-info-circle"></i>
                  <span>{{ clients.length }} clientes disponibles</span>
                </div>
              </div>
            </ng-template>
          </p-dropdown>

          <small
            class="error-message"
            *ngIf="
              incomeForm.get('clientId')?.invalid &&
              incomeForm.get('clientId')?.touched
            "
          >
            El cliente es requerido
          </small>
        </div>



        <div class="form-group">
          <label for="user">Usuario</label>
          <div class="user-display-container">
            <div class="user-info-display">
              <div class="user-icon">
                <i class="bi bi-person-circle"></i>
              </div>
              <div class="user-details">
                <span class="user-name">{{ currentUserName || 'Cargando...' }}</span>
                <span class="user-subtitle">Usuario actual</span>
              </div>
            </div>
          </div>
          <!-- Campo oculto para el userId -->
          <input
            type="hidden"
            formControlName="userId"
          />
        </div>

        <div class="form-actions">
          <button
            pButton
            type="button"
            label="Cancelar"
            class="p-button-outlined p-button-secondary"
            (click)="navigateBack()"
          ></button>
          <button
            pButton
            type="submit"
            label="Guardar"
            [disabled]="incomeForm.invalid || isLoading"
            [loading]="isLoading"
          ></button>
        </div>
      </form>
    </div>
  </div>
</div>

<p-toast></p-toast>

<!-- ✅ COMPONENTE DE DIÁLOGO GENÉRICO -->
<app-generic-dialog></app-generic-dialog>
