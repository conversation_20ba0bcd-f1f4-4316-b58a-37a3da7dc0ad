:host {
  --primary-color: #1d3c34; /* Verde oscuro */
  --primary-hover: #0f2a23; /* Verde más oscuro */
  --primary-light: #e8f0ed; /* Verde claro */
  --text-color: #2c2c2c; /* Casi negro */
  --text-light: #5f5f5f; /* Gris oscuro */
  --text-lighter: #8a8a8a; /* Gris medio */
  --background: #f5f5f0; /* Beige claro */
  --background-card: #ffffff; /* Blanco */
  --background-hover: #f0efe6; /* Beige más claro */
  --border-color: #d8d3c5; /* Beige medio */
  --accent-color: #b8860b; /* Dorado oscuro */
  --accent-light: #d4af37; /* Dorado */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;
  --transition: all 0.3s ease;
  --success-color: #198754;
  --danger-color: #dc3545;
  --info-color: #0dcaf0;
  --warning-color: #ffc107;

  display: block;
  min-height: 100vh;
  background-color: var(--background);
  padding: 1.5rem;
  font-family: "Playfair Display", Georgia, serif;
}

/* ===== LAYOUT PRINCIPAL ===== */

.income-form-container {
  max-width: 1280px;
  margin: 0 auto;
}

.content-wrapper {
  background-color: var(--background-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* ===== HEADER SECTION ===== */

.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.75rem;
  background: linear-gradient(to right, rgba(29, 60, 52, 0.02), rgba(29, 60, 52, 0.05));
  border-bottom: 1px solid var(--border-color);
}

.title-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.title-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius-full);
}

.page-title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-color);
  letter-spacing: 0.3px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border: none;
  border-radius: var(--radius);
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
  margin-right: 1rem;
}

.back-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius);
}

.back-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.back-button:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button i {
  font-size: 1.25rem;
}

/* ===== FORM CONTAINER ===== */

.form-container {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--text-color);
  font-family: "Playfair Display", Georgia, serif;
  letter-spacing: 0.3px;
  font-size: 1.05rem;
}

/* ===== AMOUNT INPUT CONTAINER ===== */

.amount-input-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.money-counter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  color: white;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  font-size: 1.25rem;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.money-counter-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius-full);
}

.money-counter-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

.money-counter-button:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.money-counter-button i {
  font-size: 1.25rem;
  z-index: 1;
  position: relative;
}

/* ===== INPUT STYLES ===== */

:host ::ng-deep .p-inputtext,
:host ::ng-deep .p-dropdown {
  width: 100%;
  padding: 1rem;
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
  background-color: var(--background);
  font-size: 1rem;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-family: "Playfair Display", Georgia, serif;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

:host ::ng-deep .p-inputtext:focus,
:host ::ng-deep .p-dropdown:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(29, 60, 52, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0);
  background-color: var(--background-card);
}

:host ::ng-deep .p-inputtext::placeholder {
  color: var(--text-lighter);
  font-family: "Playfair Display", Georgia, serif;
}

/* Panel principal del dropdown con máxima especificidad */
.p-dropdown-panel.p-component {
  background: #ffffff !important;
  border: 2px solid #d8d3c5 !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  margin-top: 8px !important;
  z-index: 10000 !important;
  overflow: hidden !important;
  min-width: 300px !important;
  animation: dropdownSlideIn 0.3s ease-out !important;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Resetear y limpiar la lista de items */
.p-dropdown-panel .p-dropdown-items {
  padding: 0 !important;
  margin: 0 !important;
  background: #ffffff !important;
  list-style: none !important;
}

/* Items individuales - reseteo completo */
.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  background: #ffffff !important;
  color: #333333 !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  transition: all 0.3s ease !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
  overflow: visible !important;
}

/* Estados hover y seleccionado para items */
.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover {
  background: #f0f8f6 !important;
  color: #1d3c34 !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
  background: #1d3c34 !important;
  color: #ffffff !important;
}

/* ===== CLASES PROFESIONALES PERSONALIZADAS ===== */

/* Header del dropdown */
.dropdown-header-professional {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 16px 20px !important;
  background: linear-gradient(135deg, #1d3c34, #0f2a23) !important;
  color: #ffffff !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  margin: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.dropdown-header-professional .header-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  font-size: 16px !important;
  color: #ffffff !important;
}

.dropdown-header-professional .header-content {
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
}

.dropdown-header-professional .header-title {
  font-weight: 600 !important;
  font-size: 15px !important;
  line-height: 1.2 !important;
  color: #ffffff !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

.dropdown-header-professional .header-subtitle {
  font-size: 12px !important;
  opacity: 0.8 !important;
  font-weight: 400 !important;
  color: #ffffff !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

/* Footer del dropdown */
.dropdown-footer-professional {
  padding: 12px 20px !important;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
  border-top: 1px solid #d8d3c5 !important;
  margin: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.dropdown-footer-professional .footer-info {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-size: 12px !important;
  color: #5f5f5f !important;
  font-weight: 500 !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

.dropdown-footer-professional .footer-info i {
  font-size: 14px !important;
  color: #b8860b !important;
}

/* Item seleccionado en el campo principal */
.selected-category-professional {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 4px 0 !important;
  background: transparent !important;
  width: 100% !important;
}

.selected-category-professional .category-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  background: linear-gradient(135deg, #b8860b, #d4af37) !important;
  border-radius: 8px !important;
  color: #ffffff !important;
  font-size: 14px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
}

.selected-category-professional .category-info {
  display: flex !important;
  flex-direction: column !important;
  gap: 2px !important;
  flex-grow: 1 !important;
}

.selected-category-professional .category-name {
  font-weight: 600 !important;
  color: #333333 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

.selected-category-professional .category-type {
  font-size: 11px !important;
  color: #8a8a8a !important;
  font-weight: 400 !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

/* Items en la lista del dropdown */
.category-item-professional {
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  padding: 12px 16px !important;
  background: #ffffff !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  border-radius: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  border-bottom: 1px solid #f0f0f0 !important;
  position: relative !important;
}

/* Hover effect para items */
.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover .category-item-professional {
  background: #f0f8f6 !important;
  transform: translateX(4px) !important;
}

/* Item seleccionado */
.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight .category-item-professional {
  background: #1d3c34 !important;
  transform: none !important;
}

.category-item-professional .category-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 36px !important;
  height: 36px !important;
  background: linear-gradient(135deg, #b8860b, #d4af37) !important;
  border-radius: 10px !important;
  color: #ffffff !important;
  font-size: 16px !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight .category-item-professional .category-icon {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.category-item-professional .category-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
}

.category-item-professional .category-name {
  font-weight: 600 !important;
  color: #333333 !important;
  font-size: 15px !important;
  line-height: 1.3 !important;
  font-family: "Playfair Display", Georgia, serif !important;
  margin: 0 !important;
  padding: 0 !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover .category-item-professional .category-name {
  color: #1d3c34 !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight .category-item-professional .category-name {
  color: #ffffff !important;
}

.category-item-professional .category-description {
  font-size: 12px !important;
  color: #8a8a8a !important;
  line-height: 1.4 !important;
  font-family: "Playfair Display", Georgia, serif !important;
  margin: 0 !important;
  padding: 0 !important;
  max-width: 200px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover .category-item-professional .category-description {
  color: #5f5f5f !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight .category-item-professional .category-description {
  color: rgba(255, 255, 255, 0.8) !important;
}

.category-item-professional .category-arrow {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  color: #8a8a8a !important;
  font-size: 12px !important;
  transition: all 0.3s ease !important;
  opacity: 0 !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover .category-item-professional .category-arrow {
  opacity: 1 !important;
  color: #1d3c34 !important;
  transform: translateX(2px) !important;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight .category-item-professional .category-arrow {
  color: #ffffff !important;
  opacity: 1 !important;
}

/* ===== FILTRO DEL DROPDOWN (BUSCADOR) ===== */
.p-dropdown-filter {
  padding: 12px 16px !important;
  border: 2px solid #d8d3c5 !important;
  border-radius: 8px !important;
  background: #ffffff !important; /* ✅ FONDO BLANCO */
  color: #000000 !important; /* ✅ TEXTO NEGRO */
  font-size: 14px !important;
  font-family: "Playfair Display", Georgia, serif !important;
  font-weight: 500 !important;
  margin: 8px 12px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s ease !important;
}

.p-dropdown-filter:focus {
  outline: none !important;
  border-color: #1d3c34 !important;
  box-shadow: 0 0 0 3px rgba(29, 60, 52, 0.1), 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  background: #ffffff !important; /* ✅ MANTENER FONDO BLANCO EN FOCUS */
  color: #000000 !important; /* ✅ MANTENER TEXTO NEGRO EN FOCUS */
}

.p-dropdown-filter::placeholder {
  color: #666666 !important; /* ✅ PLACEHOLDER GRIS OSCURO PARA BUENA VISIBILIDAD */
  font-family: "Playfair Display", Georgia, serif !important;
  font-weight: 400 !important;
}

/* ===== ESTILOS PARA CAMPOS DE USUARIO Y FECHA ===== */

/* Contenedor para mostrar información del usuario */
.user-display-container {
  width: 100%;
  padding: 1rem;
  border-radius: var(--radius-md);
  border: 2px solid var(--border-color);
  background: linear-gradient(135deg, var(--background), var(--background-hover));
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
}

.user-info-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.user-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: var(--radius-full);
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex-grow: 1;
}

.user-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
  line-height: 1.2;
  font-family: "Playfair Display", Georgia, serif;
}

.user-subtitle {
  font-size: 0.875rem;
  color: var(--text-lighter);
  font-weight: 400;
  font-family: "Playfair Display", Georgia, serif;
}



/* ===== ESTILOS PARA MENSAJES VACÍOS ===== */

.empty-message-professional {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 20px !important;
  text-align: center !important;
  color: #8a8a8a !important;
  background: #ffffff !important;
}

.empty-message-professional .empty-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  background: #f8f9fa !important;
  border-radius: 50% !important;
  color: #8a8a8a !important;
  font-size: 18px !important;
  margin-bottom: 4px !important;
}

.empty-message-professional .empty-text {
  font-weight: 500 !important;
  font-size: 14px !important;
  color: #5f5f5f !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

.empty-message-professional .empty-subtext {
  font-size: 12px !important;
  color: #8a8a8a !important;
  font-family: "Playfair Display", Georgia, serif !important;
}

/* ===== ERROR MESSAGE ===== */

.error-message {
  color: var(--danger-color);
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: "Playfair Display", Georgia, serif;
  letter-spacing: 0.2px;
}

/* ===== FORM ACTIONS ===== */

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1.25rem;
  margin-top: 2.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

/* ===== PRIMENG BUTTON OVERRIDES ===== */

:host ::ng-deep .p-button {
  font-family: "Playfair Display", Georgia, serif;
  font-weight: 600;
  padding: 0.875rem 1.75rem;
  border-radius: var(--radius);
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

:host ::ng-deep .p-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  border-radius: var(--radius);
}

:host ::ng-deep .p-button:not(.p-button-outlined):not(:disabled) {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  border: none;
  color: white;
  font-size: 1rem;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
}

:host ::ng-deep .p-button:not(.p-button-outlined):not(:disabled):hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 3px 6px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .p-button:not(.p-button-outlined):not(:disabled):active {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .p-button.p-button-outlined {
  color: var(--text-color);
  border: 2px solid var(--border-color);
  background: var(--background-card);
  font-weight: 600;
}

:host ::ng-deep .p-button.p-button-outlined:hover {
  background: var(--background-hover);
  color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .p-button.p-button-outlined:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
/* ===== RESPONSIVE STYLES ===== */

@media (max-width: 992px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .form-container {
    padding: 1.5rem;
  }

  .title-container {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  :host {
    padding: 1rem;
  }

  .form-container {
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .form-actions button {
    width: 100%;
  }

  .user-info-display {
    gap: 0.75rem;
  }

  .user-icon {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.25rem;
  }

  .user-name {
    font-size: 0.9rem;
  }

  .user-subtitle {
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .header-section {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .title-icon {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
  }

  .back-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  .form-group label {
    font-size: 1rem;
  }

  /* Ajustes para dropdowns en móvil */
  .p-dropdown-panel.p-component {
    min-width: 280px !important;
    max-width: 90vw !important;
  }

  .category-item-professional {
    padding: 10px 12px !important;
  }

  .category-item-professional .category-icon {
    width: 32px !important;
    height: 32px !important;
    font-size: 14px !important;
  }

  .category-item-professional .category-name {
    font-size: 14px !important;
  }

  .category-item-professional .category-description {
    font-size: 11px !important;
    max-width: 150px !important;
  }
}