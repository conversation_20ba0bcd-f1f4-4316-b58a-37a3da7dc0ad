import {
  Component,
  OnInit,
  OnDestroy,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputNumberModule } from 'primeng/inputnumber';
import { CalendarModule } from 'primeng/calendar';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { TooltipModule } from 'primeng/tooltip';
import { Client } from '../../../../dtos/client.model';
import { ClientService } from '../../../../services/client.service';
import { NavigationService } from '../../../../services/navigation.service';
import { HeaderService } from '../../../../services/header.service';
import { UserService } from '../../../../services/user.service';
import { DialogService } from '../../../../services/dialog.service';
import { GenericDialogComponent } from '../../../shared/generic-dialog/generic-dialog.component';

@Component({
  selector: 'app-client-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    InputNumberModule,
    CalendarModule,
    ToastModule,
    TooltipModule,
    GenericDialogComponent
  ],
  providers: [MessageService],
  templateUrl: './client-form.component.html',
  styleUrl: './client-form.component.css',
})
export class ClientFormComponent implements OnInit, OnDestroy {
  @Input() client: any = null;
  @Output() formSubmit = new EventEmitter<any>();
  @Output() cancelForm = new EventEmitter<void>();

  clientForm: FormGroup;
  isLoading: boolean = false;
  isEditMode: boolean = false;
  clientId: number | null = null;
  currentUser: any = null;
  currentUserName: string = '';

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private clientService: ClientService,
    private messageService: MessageService,
    private navigationService: NavigationService,
    private headerService: HeaderService,
    private userService: UserService,
    private dialogService: DialogService
  ) {
    this.clientForm = this.fb.group({
      companyName: ['', [Validators.required, Validators.maxLength(255)]],
      taxId: ['', [Validators.required, Validators.maxLength(50)]],
      businessName: ['', [Validators.required, Validators.maxLength(255)]],
      verificationNumber: ['', [Validators.required, Validators.maxLength(10)]],
      contactName: ['', [Validators.required, Validators.maxLength(255)]],
      address: ['', [Validators.required, Validators.maxLength(500)]],
      countryCode: ['', [Validators.required, Validators.maxLength(10)]],
      phone: ['', [Validators.required, Validators.maxLength(20)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(255)]],
      birthDate: [null, Validators.required],
      vatRate: [0, [Validators.required, Validators.min(0), Validators.max(100)]],
      consumptionTaxRate: [0, [Validators.required, Validators.min(0), Validators.max(100)]],
      allowedUsers: [1, [Validators.required, Validators.min(1)]],
      userId: [null], // Se establecerá automáticamente
      isActive: [true] // Solo para modo edición
    });
  }

  ngOnInit(): void {
    // Verificar si estamos en modo edición comprobando la ruta
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.clientId = +id;
        this.isEditMode = true;
        console.log('Modo edición activado para client ID:', this.clientId);
      }

      // Cargar información del usuario logueado primero
      this.loadCurrentUser();
    });
  }

  ngOnDestroy(): void {
    // Limpiar cualquier recurso si es necesario
  }

  // Método para cargar la información del usuario logueado
  loadCurrentUser(): void {
    const userInfo = this.headerService.getCurrentUser();
    if (userInfo && userInfo.email) {
      // Buscar el usuario completo por email para obtener el ID
      this.userService.searchUserByEmail(userInfo.email).subscribe({
        next: (response) => {
          if (response.code === 200 && response.data) {
            this.currentUser = response.data;
            this.currentUserName = `${this.currentUser.firstName} ${this.currentUser.lastName}`;

            // Establecer el userId en el formulario siempre (tanto en creación como edición)
            this.clientForm.patchValue({
              userId: this.currentUser.id,
            });

            console.log('Usuario actual cargado:', this.currentUser);
            console.log('UserId establecido en formulario:', this.currentUser.id);

            // DESPUÉS de cargar el usuario, cargar el client si estamos en modo edición
            if (this.isEditMode && this.clientId) {
              console.log('Cargando client para edición...');
              this.loadClient(this.clientId);
            }
          } else {
            this.dialogService.showError('Error al cargar usuario', 'No se pudo obtener la información del usuario logueado');
          }
        },
        error: (err) => {
          this.dialogService.showError('Error al cargar usuario', 'No se pudo obtener la información del usuario logueado');
          console.error('Error loading current user:', err);
        },
      });
    } else {
      this.dialogService.showError('Error de autenticación', 'No se pudo obtener la información del usuario logueado');
    }
  }

  loadClient(id: number): void {
    console.log('Iniciando carga de client con ID:', id);
    this.isLoading = true;

    this.clientService.getClientById(id).subscribe({
      next: (response) => {
        console.log('Respuesta del servidor al cargar client:', response);

        if (response.code === 200) {
          const client = response.data;
          console.log('Client cargado:', client);

          // Cargar los datos en el formulario
          this.clientForm.patchValue({
            companyName: client.companyName,
            taxId: client.taxId,
            businessName: client.businessName,
            verificationNumber: client.verificationNumber,
            contactName: client.contactName,
            address: client.address,
            countryCode: client.countryCode,
            phone: client.phone,
            email: client.email,
            birthDate: new Date(client.birthDate),
            vatRate: client.vatRate,
            consumptionTaxRate: client.consumptionTaxRate,
            allowedUsers: client.allowedUsers,
            userId: this.currentUser?.id || client.userId,
            isActive: client.isActive
          });

          console.log('Formulario actualizado con datos del client');
          console.log('Valores del formulario:', this.clientForm.value);
        } else {
          this.dialogService.showError('Error al cargar cliente', response.message as string);
          this.navigateToClients();
        }
        this.isLoading = false;
      },
      error: (err) => {
        this.dialogService.showError('Error al cargar cliente', 'No se pudo cargar el cliente');
        this.isLoading = false;
        console.error('Error loading client:', err);
        this.navigateToClients();
      },
    });
  }

  async saveClient(): Promise<void> {
    // Verificar que el usuario actual esté cargado
    if (!this.currentUser || !this.currentUser.id) {
      this.dialogService.showError('Error de usuario', 'No se ha cargado la información del usuario. Por favor, recargue la página.');
      return;
    }

    // Asegurar que el userId esté establecido en el formulario
    if (!this.clientForm.get('userId')?.value) {
      this.clientForm.patchValue({
        userId: this.currentUser.id,
      });
    }

    if (this.clientForm.valid) {
      const clientData = this.clientForm.value;

      // Verificación adicional antes de enviar
      if (!clientData.userId) {
        clientData.userId = this.currentUser.id;
      }

      // Formatear la fecha de nacimiento
      if (clientData.birthDate) {
        clientData.birthDate = this.formatDateForAPI(clientData.birthDate);
      }

      console.log('Datos del cliente a enviar:', clientData);

      // Crear descripción detallada para la confirmación
      const clientDescription = `el cliente "${clientData.companyName}" (${clientData.taxId})`;
      
      // Mostrar diálogo de confirmación antes de guardar
      let confirmed = false;
      if (this.isEditMode && this.clientId) {
        confirmed = await this.dialogService.showUpdateConfirmation('cliente', clientDescription);
      } else {
        confirmed = await this.dialogService.showCreateConfirmation('cliente', clientDescription);
      }

      if (!confirmed) {
        return; // El usuario canceló la operación
      }

      this.isLoading = true;

      if (this.isEditMode && this.clientId) {
        // Actualizar cliente existente
        this.clientService.updateClient(this.clientId, clientData).subscribe({
          next: (response) => {
            if (response.code === 200 || response.code === 201 || 
                (response.message && response.message.toLowerCase().includes('success'))) {
              this.dialogService.showUpdateSuccess('cliente');
              this.navigateToClients();
            } else {
              this.dialogService.showUpdateError('cliente', response.message as string);
            }
            this.isLoading = false;
          },
          error: (err) => {
            this.dialogService.showUpdateError('cliente', 'No se pudo actualizar el cliente');
            this.isLoading = false;
            console.error('Error updating client:', err);
          },
        });
      } else {
        // Crear nuevo cliente
        this.clientService.createClient(clientData).subscribe({
          next: (response) => {
            if (response.code === 200 || response.code === 201 || 
                (response.message && response.message.toLowerCase().includes('success'))) {
              this.dialogService.showCreateSuccess('cliente');
              this.navigateToClients();
            } else {
              this.dialogService.showCreateError('cliente', response.message as string);
            }
            this.isLoading = false;
          },
          error: (err) => {
            this.dialogService.showCreateError('cliente', 'No se pudo crear el cliente');
            this.isLoading = false;
            console.error('Error creating client:', err);
          },
        });
      }
    } else {
      // Marcar todos los campos como tocados para mostrar errores
      Object.keys(this.clientForm.controls).forEach((key) => {
        this.clientForm.get(key)?.markAsTouched();
      });
      this.dialogService.showWarning('Por favor, complete todos los campos requeridos');
    }
  }

  formatDateForAPI(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  navigateToClients(): void {
    setTimeout(() => {
      this.router.navigate(['/main-cloud/clients']);
      console.log('Navegando a la lista de clients');
    }, 300);
  }

  navigateBack(): void {
    this.navigateToClients();
  }
}
