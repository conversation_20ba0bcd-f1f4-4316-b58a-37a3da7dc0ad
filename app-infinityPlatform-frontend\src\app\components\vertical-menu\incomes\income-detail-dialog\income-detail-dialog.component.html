<p-dialog [(visible)]="visible" [modal]="true" [draggable]="false" [resizable]="false" [style]="{width: '550px'}"
  header="Detalles del Ingreso" styleClass="income-detail-dialog" [closeOnEscape]="true" [dismissableMask]="true"
  (onHide)="visibleChange.emit(false)">
  
  <ng-container *ngIf="income">
    <div class="income-detail-container">
      <!-- Encabezado con ID -->
      <div class="income-header">
        <div class="income-id">
        </div>
      </div>
      
      <!-- Segmento de fechas - DESPLEGABLE -->
      <div class="collapsible-section">
        <div class="collapsible-header" (click)="toggleTemporalInfo()">
          <div class="section-title">Información temporal</div>
          <div class="collapsible-icon" [class.expanded]="temporalInfoExpanded">
            <i class="bi bi-chevron-down"></i>
          </div>
        </div>
        
        <div class="collapsible-content" [class.expanded]="temporalInfoExpanded">
          <div class="income-dates-section">
            <!-- Fecha de creación -->
            <div class="income-detail-item">
              <div class="detail-label">Fecha de creación:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-1">
                  <i class="bi bi-calendar"></i>
                </div>
                <span>{{ income.date | date:'dd/MM/yyyy HH:mm' }}</span>
              </div>
            </div>

            <!-- Fecha de actualización (si existe) -->
            <div class="income-detail-item">
              <div class="detail-label">Última actualización:</div>
              <div class="detail-value">
                <div class="link-icon bg-gradient-elegant-5">
                  <i class="bi bi-clock-history"></i>
                </div>
                <span *ngIf="income.updateDate">{{ income.updateDate | date:'dd/MM/yyyy HH:mm' }}</span>
                <span *ngIf="!income.updateDate" class="no-data">No actualizado</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tipo con diseño mejorado - DESPLEGABLE -->
      <div class="collapsible-section">
        <div class="collapsible-header" (click)="toggleTypeInfo()">
          <div class="section-title">Información de Tipo</div>
          <div class="collapsible-icon" [class.expanded]="typeInfoExpanded">
            <i class="bi bi-chevron-down"></i>
          </div>
        </div>
        
        <div class="collapsible-content" [class.expanded]="typeInfoExpanded">
          <div class="income-type-section">
            <div class="type-content">
              <div class="type-icon">
                <i class="bi bi-tag"></i>
              </div>
              <div class="type-info-container">
                <div class="type-name">{{ income.incomeType?.name }}</div>
                <div class="type-details-table">
                  <div class="detail-key">Tipo:</div>
                  <div class="detail-value">Ingreso</div>
                </div>
                <div class="type-description" *ngIf="income.incomeType?.description">
                  {{ income.incomeType?.description }}
                </div>
                <div class="no-description" *ngIf="!income.incomeType?.description">
                  Sin descripción disponible
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Monto -->
      <div class="income-detail-item">
        <div class="detail-label">Monto:</div>
        <div class="detail-value amount">
          <div class="link-icon bg-gradient-elegant-2">
            <i class="bi bi-currency-dollar"></i>
          </div>
          <span>{{ income.amount | currency:'USD':'symbol':'1.2-2' }}</span>
        </div>
      </div>
      
      <!-- Cliente -->
      <div class="income-detail-item">
        <div class="detail-label">Cliente:</div>
        <div class="detail-value">
          <div class="link-icon bg-gradient-elegant-3">
            <i class="bi bi-building"></i>
          </div>
          <span>{{ getClientName() }}</span>
        </div>
      </div>

      <!-- Usuario que creó el ingreso -->
      <div class="income-detail-item">
        <div class="detail-label">Creado por:</div>
        <div class="detail-value">
          <div class="link-icon bg-gradient-elegant-5">
            <i class="bi bi-person"></i>
          </div>
          <span>{{ getUserName() }}</span>
        </div>
      </div>

      <!-- Descripción -->
      <div class="income-detail-item">
        <div class="detail-label">Descripción:</div>
        <div class="detail-value">
          <div class="link-icon bg-gradient-elegant-4">
            <i class="bi bi-card-text"></i>
          </div>
          <span>{{ income.description || 'Sin descripción' }}</span>
        </div>
      </div>
    </div>
  </ng-container>
  
  <ng-template pTemplate="footer">
    <div class="dialog-footer">
      <button class="close-button" (click)="hideDialog()">
        <i class="bi bi-x"></i>
        Cerrar
      </button>
      <button class="edit-button" pTooltip="Editar ingreso" tooltipPosition="top" (click)="editIncome()">
        <i class="bi bi-pencil"></i>
        Editar
      </button>
    </div>
  </ng-template>
</p-dialog>




