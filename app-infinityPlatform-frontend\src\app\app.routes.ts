import { Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { LoginComponent } from './components/login/login.component';
import { MainCloudComponent } from './components/main-cloud/main-cloud.component';
import { QuickLinksComponent } from './components/quick-links/quick-links.component';
import { ClientsComponent } from './components/vertical-menu/clients/clients.component';
import { ClientFormComponent } from './components/vertical-menu/clients/client-form/client-form.component';
import { UsersComponent } from './components/vertical-menu/users/users.component';
import { ExpensesComponent } from './components/vertical-menu/expenses/expenses.component';
import { ExpenseFormComponent } from './components/vertical-menu/expenses/expense-form/expense-form.component';
import { IncomesComponent } from './components/vertical-menu/incomes/incomes.component';
import { IncomeFormComponent } from './components/vertical-menu/incomes/income-form/income-form.component';
import { SubscriptionsComponent } from './components/subscriptions/subscriptions.component'; // Verificar esta ruta
import { SubscriptionFormComponent } from './components/subscriptions/subscription-form/subscription-form.component';
import { IncidentsComponent } from './components/vertical-menu/incidents/incidents.component';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'login',
    component: LoginComponent,
  },
  {
    path: 'main-cloud',
    component: MainCloudComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        redirectTo: 'quick-links',
        pathMatch: 'full'
      },
      {
        path: 'quick-links',
        component: QuickLinksComponent,
      },
      {
        path: 'clients',
        component: ClientsComponent,
      },
      {
        path: 'clients/create',
        component: ClientFormComponent,
      },
      {
        path: 'clients/edit/:id',
        component: ClientFormComponent,
      },
      {
        path: 'users',
        component: UsersComponent,
      },
      {
        path: 'expenses',
        component: ExpensesComponent,
      },
      {
        path: 'expenses/create',
        component: ExpenseFormComponent,
      },
      {
        path: 'expenses/edit/:id',
        component: ExpenseFormComponent,
      },
      {
        path: 'incomes',
        component: IncomesComponent,
      },
      {
        path: 'incomes/create',
        component: IncomeFormComponent,
      },
      {
        path: 'incomes/edit/:id',
        component: IncomeFormComponent,
      },
      {
        path: 'subscriptions', // Esta ruta debe estar aquí
        component: SubscriptionsComponent,
      },
      {
        path: 'subscriptions/create',
        component: SubscriptionFormComponent,
      },
      {
        path: 'subscriptions/edit/:id',
        component: SubscriptionFormComponent,
      },
      {
        path: 'incidents', // Esta ruta debe estar aquí
        component: IncidentsComponent ,
      }
      // ⚠️ DESCOMENTA ESTAS CUANDO CREES IncidentFormComponent:
      // {
      //   path: 'incidents/create',
      //   component: IncidentFormComponent, // Formulario para crear
      // },
      // {
      //   path: 'incidents/edit/:id',
      //   component: IncidentFormComponent, // Formulario para editar
      // }
    ]
  }
];