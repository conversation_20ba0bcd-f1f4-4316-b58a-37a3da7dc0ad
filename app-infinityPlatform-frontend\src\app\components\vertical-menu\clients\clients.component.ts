import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { MessageService } from 'primeng/api';
import { Client } from '../../../dtos/client.model';
import { ClientService } from '../../../services/client.service';
import { NavigationService } from '../../../services/navigation.service';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ClientDetailDialogComponent } from './client-detail-dialog/client-detail-dialog.component';

@Component({
  selector: 'app-clients',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    ToastModule,
    TooltipModule,
    ClientDetailDialogComponent
],
  providers: [MessageService],
  templateUrl: './clients.component.html',
  styleUrl: './clients.component.css'
})
export class ClientsComponent implements OnInit, OnDestroy {
  // Agregamos Math para usarlo en la plantilla
  Math = Math;
  
  clients: Client[] = [];
  allClients: Client[] = []; 
  filteredClients: Client[] = []; // Nueva propiedad para mantener resultados filtrados
  loading: boolean = true;
  searchTerm: string = '';
  statusFilter: 'all' | 'active' | 'inactive' = 'all';
  
  // Configuración de paginación mejorada
  rowsPerPageOptions: number[] = [8, 14, 25, 50];
  rowsPerPage: number = 8;
  totalRecords: number = 0;
  pageIndex: number = 0;
  
  // Para implementar el debounce
  private searchTerms = new Subject<string>();
  private searchSubscription: Subscription | null = null;

  constructor(
    private clientService: ClientService,
    private messageService: MessageService,
    private navigationService: NavigationService
  ) { }

  ngOnInit(): void {
    this.loadClients();
    
    // Configurar el debounce para la búsqueda
    this.searchSubscription = this.searchTerms.pipe(
      // Espera 300ms después de cada pulsación de tecla
      debounceTime(300),
      // Ignora si el término es el mismo que el anterior
      distinctUntilChanged()
    ).subscribe(() => {
      this.applyFilters();
    });
  }
  
  ngOnDestroy(): void {
    // Limpia la suscripción para evitar memory leaks
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
  }

  // Este método se llamará cada vez que el usuario escriba en el campo de búsqueda
  onSearchInput(term: string): void {
    this.searchTerm = term;
    this.searchTerms.next(term);
  }

  loadClients(): void {
    this.loading = true;

    // Determinar el parámetro isActive basado en el filtro actual
    let isActiveParam: boolean | undefined = undefined;
    if (this.statusFilter === 'active') {
      isActiveParam = true;
    } else if (this.statusFilter === 'inactive') {
      isActiveParam = false;
    }
    // Si statusFilter es 'all', isActiveParam permanece undefined

    this.clientService.getAllClients(isActiveParam).subscribe({
      next: (response) => {
        if (response.code === 200) {
          this.allClients = response.data;
          this.applyFilters();
          this.loading = false;
        } else {
          this.showError('Error al cargar clientes', response.message as string);
        }
      },
      error: (err) => {
        this.loading = false;
        this.showError('Error al cargar clientes', 'No se pudieron cargar los clientes. Intente nuevamente.');
        console.error('Error loading clients:', err);
      }
    });
  }

  applyStatusFilter(status: 'all' | 'active' | 'inactive'): void {
    this.statusFilter = status;
    this.pageIndex = 0; // Volver a la primera página al cambiar filtros
    this.loadClients(); // Recargar clientes con el nuevo filtro

    let statusText = status === 'all' ? 'todos' : status === 'active' ? 'activos' : 'inactivos';
    this.messageService.add({
      severity: 'info',
      summary: 'Filtro aplicado',
      detail: `Mostrando clientes ${statusText}`
    });
  }

  // Método mejorado para filtrar y paginar
  applyFilters(): void {
    let filteredResults = this.allClients;

    // Solo aplicar filtro de búsqueda por texto, el filtro de estado se maneja en la API
    if (this.searchTerm.trim()) {
      const searchTermLower = this.searchTerm.toLowerCase().trim();
      filteredResults = filteredResults.filter(client =>
        (client.companyName?.toLowerCase() || '').includes(searchTermLower) ||
        (client.contactName?.toLowerCase() || '').includes(searchTermLower) ||
        (client.taxId?.toLowerCase() || '').includes(searchTermLower) ||
        (client.email?.toLowerCase() || '').includes(searchTermLower) ||
        (client.countryCode?.toLowerCase() || '').includes(searchTermLower) ||
        (client.phone?.toLowerCase() || '').includes(searchTermLower)
      );
    }

    this.filteredClients = filteredResults;
    this.totalRecords = filteredResults.length;

    // Si estamos en una página que ya no existe después del filtrado, volvemos a la primera
    if (this.pageIndex > this.getMaxPage()) {
      this.pageIndex = 0;
    }

    this.updateDisplayedClients();
  }

  // Método para actualizar los registros mostrados según la página actual
  updateDisplayedClients(): void {
    const startIndex = this.pageIndex * this.rowsPerPage;
    const endIndex = Math.min(startIndex + this.rowsPerPage, this.totalRecords);
    this.clients = this.filteredClients.slice(startIndex, endIndex);
  }

  // Método para navegar a una página específica
  goToPage(page: number): void {
    if (page < 0 || page > this.getMaxPage()) {
      return;
    }
    
    this.pageIndex = page;
    this.updateDisplayedClients();
  }

  // Método para obtener el índice de la última página
  getMaxPage(): number {
    return Math.max(0, Math.ceil(this.totalRecords / this.rowsPerPage) - 1);
  }

  // Método para manejar cambios en filas por página
  onRowsPerPageChange(event: any): void {
    this.rowsPerPage = parseInt(event.target.value, 10);
    this.pageIndex = 0; // Volvemos a la primera página
    this.updateDisplayedClients();
  }

  // Método para generar el array de números de página a mostrar
  getPageNumbers(): (number | string)[] {
    const totalPages = this.getMaxPage() + 1;
    const currentPage = this.pageIndex + 1;
    const pageNumbers: (number | string)[] = [];
    
    if (totalPages <= 7) {
      // Si hay 7 o menos páginas, mostramos todas
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Siempre incluimos la primera página
      pageNumbers.push(1);
      
      // Si la página actual está cerca del inicio
      if (currentPage <= 3) {
        pageNumbers.push(2, 3, 4, '...', totalPages);
      } 
      // Si la página actual está cerca del final
      else if (currentPage >= totalPages - 2) {
        pageNumbers.push('...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
      } 
      // Si la página actual está en el medio
      else {
        pageNumbers.push('...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
      }
    }
    
    return pageNumbers;
  }

  createNewClient(): void {
    this.navigationService.gotoCreateClient();
  }

  showError(summary: string, detail: string): void {
    this.messageService.add({
      severity: 'error',
      summary: summary,
      detail: detail
    });
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.statusFilter = 'all';
    this.pageIndex = 0; // Volvemos a la primera página
    this.loadClients();
  }

  navigateTo(route: string): void {
    if (route === 'quick-links') {
      this.navigationService.gotoQuickLinks();
    }
  }

  selectedClient: Client | null = null;
  showClientDialog: boolean = false;

  showClientDetails(client: Client): void {
    this.selectedClient = client;
    this.showClientDialog = true;
  }

  editClient(client: Client, event: Event): void {
    event.stopPropagation(); // Evitar que se abra el diálogo de detalles
    console.log('Editar cliente:', client);
    this.navigationService.gotoCreateUpdateClient(client.clientId);
  }

  toggleClientStatus(client: Client, event: Event): void {
    event.stopPropagation(); // Evitar que se abra el diálogo de detalles
    const action = client.isActive ? 'desactivar' : 'activar';
    console.log(`${action} cliente:`, client);
    // Implementar la lógica para cambiar el estado del cliente
    this.messageService.add({
      severity: 'info',
      summary: 'Información',
      detail: `Funcionalidad de ${action} cliente pendiente de implementar`
    });
  }

  handleEditRequest(client: Client): void {
    // Implementar la navegación a la página de edición
    console.log('Editar cliente desde diálogo:', client);
    this.navigationService.gotoCreateUpdateClient(client.clientId);
  }
}

