import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { Client } from '../../../../dtos/client.model';
import { NavigationService } from '../../../../services/navigation.service';

@Component({
  selector: 'app-client-detail-dialog',
  templateUrl: 'client-detail-dialog.component.html',
  styleUrls: ['client-detail-dialog.component.css'],
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule, TooltipModule]
})
export class ClientDetailDialogComponent implements OnInit {
  @Input() visible: boolean = false;
  @Input() client: Client | null = null;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() editRequest = new EventEmitter<Client>();

  // Variables para controlar el estado de las secciones desplegables
  contactInfoExpanded: boolean = false;
  fiscalInfoExpanded: boolean = false;
  datesInfoExpanded: boolean = false;

  constructor(
    private navigationService: NavigationService
  ) { }
  
  ngOnInit(): void {
  }
  
  // Métodos para alternar las secciones desplegables
  toggleContactInfo(): void {
    this.contactInfoExpanded = !this.contactInfoExpanded;
  }
  
  toggleFiscalInfo(): void {
    this.fiscalInfoExpanded = !this.fiscalInfoExpanded;
  }
  
  toggleDatesInfo(): void {
    this.datesInfoExpanded = !this.datesInfoExpanded;
  }
  
  hideDialog(): void {
    this.visible = false;
    this.visibleChange.emit(false);
    
    // Resetear el estado de las secciones expandibles al cerrar
    this.contactInfoExpanded = false;
    this.fiscalInfoExpanded = false;
    this.datesInfoExpanded = false;
  }

  // Método para editar el cliente actual
  editClient(): void {
    if (this.client) {
      // Cerrar el diálogo
      this.hideDialog();
      
      // Navegar a la página de edición con el ID del cliente
      // Nota: Debes implementar este método en NavigationService
      // this.navigationService.gotoCreateUpdateClient(this.client.clientId);
      
      // Alternativa: emitir evento para que el componente padre maneje la edición
      this.editRequest.emit(this.client);
    }
  }
}